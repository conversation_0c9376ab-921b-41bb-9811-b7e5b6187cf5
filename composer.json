{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-simplexml": "*", "bezhansalleh/filament-shield": "^3.2", "eightynine/filament-excel-import": "^3.0", "filament/filament": "^3.2", "filament/notifications": "^3.2", "filipfonal/filament-log-manager": "^2.0", "giggsey/libphonenumber-for-php": "^8.13", "jenssegers/agent": "dev-master", "laravel/framework": "^11.9", "laravel/horizon": "^5.25", "laravel/tinker": "^2.9", "nyholm/psr7": "^1.8", "phpseclib/phpseclib": "^3.0", "pxlrbt/filament-excel": "^2.3", "saade/filament-laravel-log": "^3.0", "saloonphp/saloon": "^3.10", "saloonphp/xml-wrangler": "^1.3", "spatie/laravel-data": "^4.10", "symfony/dom-crawler": "^7.1", "symfony/property-access": "^7.1", "tomatophp/filament-users": "*", "weijiajia/http-proxy-manager": "dev-main", "weijiajia/ip-address": "dev-master", "weijiajia/saloonphp-apple-client": "dev-main", "weijiajia/saloonphp-cookie-plugin": "dev-main", "ysfkaya/filament-phone-input": "^2.3"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.23", "jonpurvis/lawman": "^3.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.3", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}