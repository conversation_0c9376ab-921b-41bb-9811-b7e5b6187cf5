<?php

namespace Database\Factories;

use App\Models\Account;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountLogs>
 */
class AccountLogsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'account_id' => Account::factory(),
            'action' => $this->faker->word,
            'description' => $this->faker->sentence,
            'request' => $this->faker->text(300),
            'response' => $this->faker->text(300),
        ];
    }
}
