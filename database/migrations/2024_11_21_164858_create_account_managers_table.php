<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_managers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->constrained('account')->cascadeOnDelete();


            // JSON 数据
            $table->json('config')->nullable()->comment('配置');

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('account_managers');
    }
};
