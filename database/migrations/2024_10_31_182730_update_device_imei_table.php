<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('devices', function (Blueprint $table) {

            $table->string('imei')->comment('imei')->nullable();
            $table->string('meid')->comment('meid')->nullable();
            $table->string('serial_number')->comment('serial_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('devices', function (Blueprint $table) {
            $table->dropColumn('imei');
            $table->dropColumn('meid');
            $table->dropColumn('serial_number');
        });
    }
};
