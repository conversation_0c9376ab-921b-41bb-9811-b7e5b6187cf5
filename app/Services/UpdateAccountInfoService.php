<?php

namespace App\Services;

use App\Models\Account;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Saloon\Exceptions\Request\FatalRequestException;
use Saloon\Exceptions\Request\RequestException;
use Throwable;

use App\Models\AccountManager;
use App\Models\Devices;
use App\Models\Payment;
use Weijiajia\SaloonphpAppleClient\Integrations\AppleId\Dto\Response\Device\Device;

class UpdateAccountInfoService
{
    public function __construct(protected readonly Account $apple)
    {
    }

    public function handle(): void
    {

        $cookieJar = $this->apple->cookieJar();
        $awat = $cookieJar?->getCookieByName('awat');
        if (!$awat || $awat->isExpired()) {
            $this->apple->appleIdResource()->getAccountManagerResource()->token();

            $cookieJar->__destruct();
        }   

        if (!$this->apple->payment) {
            $this->updateOrCreatePaymentConfig();
        }

        if ($this->apple->devices->isEmpty()) {
            $this->updateOrCreateDevices();
        }

        if (!$this->apple->accountManager) {
            $this->updateOrCreateAccountManager();
        }
    }

    protected function updateOrCreateAccountManager(): void
    {
        $accountManager = $this->apple->appleIdResource()->getAccountManagerResource()->account();

        if ($accountManager?->account) {
            AccountManager::updateOrCreate(
                ['account_id' => $this->apple->id],
                $accountManager->toArray()
            );
        }
    }

    public function updateOrCreatePaymentConfig(): Payment
    {
        $primaryPaymentMethod = $this->apple->appleIdResource()
            ->getPaymentResource()
            ->getPayment()
            ->primaryPaymentMethod;

        return Payment::updateOrCreate(
            [
                'account_id' => $this->apple->id,
                'payment_id' => $primaryPaymentMethod->paymentId,
            ],
            $primaryPaymentMethod->toArray()
        );
    }

    /**
     * @return Collection
     * @throws FatalRequestException
     * @throws RequestException
     */
    public function updateOrCreateDevices(): Collection
    {
        return $this->apple->appleIdResource()
            ->getDevicesResource()
            ->getDevicesDetails()
            ->toCollection()
            ->map(fn (Device $device) => Devices::updateOrCreate(
                [
                    'account_id' => $this->apple->id,
                    'device_id'  => $device->deviceId,
                ],
                $device->toArray()
            ));
    }
}