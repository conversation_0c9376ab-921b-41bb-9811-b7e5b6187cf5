<?php

namespace App\Jobs;

use App\Services\AddSecurityVerifyPhoneService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Account;
use App\Apple\Enums\AccountStatus;
use Saloon\Exceptions\SaloonException;
use Saloon\Exceptions\Request\Statuses\UnauthorizedException;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Support\Facades\Log;
use DateTime;
use Weijiajia\SaloonphpAppleClient\Exception\StolenDeviceProtectionException;
use Filament\Notifications\Notification;
use App\Models\User;
use Filament\Notifications\Actions\Action;
use App\Filament\Resources\AccountResource\Pages\ViewAccount;

class AppleidAddSecurityVerifyPhone implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function retryUntil(): DateTime
    {
        return now()->addHours(24);
    }

    /**
     * 作业在超时前可以运行的秒数。
     * 单次尝试的超时时间。
     * @var int
     */
    public int $timeout = 60 * 10; // 10 minutes

    /**
     * 唯一任务锁
     * @return string
     */
    public function uniqueId(): string
    {
        return "appleid_add_security_verify_phone_lock_{$this->appleid->appleid}";
    }

    /**
     * 指定一个超时时间，超过该时间任务不再保持唯一
     * ShouldBeUnique 锁的生命周期。
     * uniqueFor >= (作业可尝试的次数 * 单次尝试的超时时间) + ((作业可尝试的次数 - 1) * 每次重试的延迟时间)
     * @return int
     */
    public function uniqueFor(): int
    {
        return 60 * 60 * 24;
    }

    /**
     * 定义每次重试之间的延迟（秒）。
     * @return int|array
     */
    public function backoff(): int|array
    {
        return 60 * 10;
    }

    /**
     * Create a new job instance.
     */
    public function __construct(protected readonly Account $appleid)
    {
        $this->onQueue('appleid_add_security_verify_phone');
    }

    public function handle(): void
    {
        try {
            
            $this->appleid->refresh();
            if ($this->appleid->status === AccountStatus::BIND_SUCCESS) {
                return;
            }

            if ($this->appleid->bind_phone) {
                return;
            }

            if ($this->appleid->status === AccountStatus::BIND_ING) {
                //如果有其他任务真在添加信任设备，我们需要让任务重试
                $this->release($this->backoff());
                return;
            }

            $this->appleid->update(['status' => AccountStatus::BIND_ING]);

            (new AddSecurityVerifyPhoneService($this->appleid))->handle();

            Log::info("[BindAccountPhone] Successfully bound phone for account {$this->appleid->appleid} on attempt {$this->attempts()}.");
        } catch (\Throwable $e) {

            //首先记录日志信息
            Log::error("{$e}");

            //记录日志
            $this->appleid->logs()
                ->create([
                    'action' => '添加授权号码失败', 
                    'request' => [
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]
                ]);

            //发送通知
            Notification::make()
                ->title("添加授权号码失败")
                ->body($e->getMessage())
                ->warning()
                ->actions([
                    Action::make('view')
                        ->label('查看账户')
                        ->button()
                        ->url(ViewAccount::getUrl(['record' => $this->appleid->id]), shouldOpenInNewTab: true),
                ])
                ->sendToDatabase(User::first());

            //开启设备包含账号不重试
            if($e instanceof StolenDeviceProtectionException){
                $this->appleid->update(['status' => AccountStatus::THEFT_PROTECTION]);
                return;
            }


            $this->appleid->update(['status' => AccountStatus::BIND_FAIL]);

            //如果返回401，则不重试
            if($e instanceof UnauthorizedException){
                return;
            }

            if($e instanceof SaloonException && !$e instanceof UnauthorizedException){
                //重新抛出异常让任务重试
                throw $e;
            }
            
            return;
        }
    }
}
