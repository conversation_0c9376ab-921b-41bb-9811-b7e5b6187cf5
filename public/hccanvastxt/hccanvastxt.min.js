eval(function(e,f,a,d,c,g){c=function(b){return(b<f?"":c(parseInt(b/f)))+(35<(b%=f)?String.fromCharCode(b+29):b.toString(36))};if(!"".replace(/^/,String)){for(;a--;)g[c(a)]=d[a]||c(a);d=[function(b){return g[b]}];c=function(){return"\\w+"};a=1}for(;a--;)d[a]&&(e=e.replace(new RegExp("\\b"+c(a)+"\\b","g"),d[a]));return e}('15 1A(){3 V={23:15(k){3 t=1m;t.X="#24";t.Y="\u5fae\u8f6f\u96c5\u9ed1";t.Z=20;t.25=k;t.11="26(0,0,0,0)";t.13=12;t.16=27;t.14="1r";t.18=0;t.17="";t.19=5;t.1a=5;t.1b=0;4(6(k)=="7"){k={}};4(6(k.13)!="7"&&k.13!=""){t.13=(k.13)};3 l="";1B.1C("1A","\"+""+l);4(6(k.X)!="7"&&k.X!=""){t.X=(k.X)};4(6(k.Y)!="7"&&k.Y!=""){t.Y=k.Y};4(6(k.Z)!="7"&&k.Z!=""){t.Z=k.Z};4(6(k.11)!="7"&&k.11!=""){t.11=(k.11)};4(6(k.18)!="7"&&k.18!=""){t.18=(k.18)};4(6(k.14)!="7"&&k.14!=""){t.14=(k.14)};4(6(k.16)!="7"&&k.16!=""){t.16=(k.16)};4(6(k.17)!="7"&&k.17!=""){t.17=(k.17)};4(6(k.19)!="7"){t.19=(k.19)};4(6(k.1a)!="7"){t.1a=(k.1a)};4(6(k.1b)!="7"){t.1b=(k.1b)};2a.2b.1s=15(c,x,y,d){3 e=1m;3 f=e.1n;4(!d&&f){d=1D(1E.2c(f).18)}4(!d){1e 1m.1t(c,x,y)};3 g=c.1o(\'\');3 h=e.1j||\'1r\';3 i=e.1f(c).8;3 j=i+d*(g.9-1);4(h==\'1F\'){x=x-j/2}W 4(h==\'1G\'){x=x-j};e.1j=\'1r\';g.2e(15(a){3 b=e.1f(a).8;e.1t(a,x,y);x=x+b+d});e.1j=h}},1H:15(){3 c=1k.1I("1n"),1g=c.1J("2d"),1K=1E.2f||1,1L=1g[\'2g\']||1g[\'2h\']||1g[\'2i\']||1g[\'2j\']||1g[\'2k\']||1;1e 1K/1L},2l:15(a){3 b=1p 1q("[\\\\2m-\\\\2n]+","g");4(!b.2o(a)){1e 1u}W{1e 1v}},2p:15(a){3 t=1m;3 b=1k.1I("1n");3 c=b.1J(\'2d\');3 d=t.1H();3 e={x:0,y:0};3 f=1M;3 g=1M;3 j=g;3 k=t.X;3 l=t.Y;3 m=t.Z;3 n=t.11;3 o=t.13;3 p=t.16;3 q=t.14;3 r=t.19;3 s=t.1a;3 u=t.1b;3 v=5;3 z=t.18;3 A=t.17;3 B=2q;4(6(a.Z)!="7"&&a.Z!=""){m=(a.Z)};4(6(a.17)!="7"){A=(a.17)};4(6(a.16)!="7"){p=(a.16)};3 C="1N"1O 1k?1v:1u;4(!C){d=1};4(6(a.1P)!="7"){3 B=1k.2r(a.1P);4(B){g=B.1Q};4(g==0){g=B.2s.1Q}};4(6(a.1c)!="7"&&a.1c!=""){f=1R(a.1c)};4(6(a.8)!="7"&&a.8!=""){g=1R(a.8)};4(6(a.Y)!="7"&&a.Y!=""){l=(a.Y)};4(6(a.X)!="7"&&a.X!=""){k=(a.X)};4(6(a.11)!="7"&&a.11!=""){n=(a.11)};4(6(a.13)!="7"&&a.13!=""){o=(a.13)};4(6(a.19)!="7"){r=(a.19)};4(6(a.1a)!="7"){s=(a.1a)};4(6(a.1b)!="7"){u=(a.1b)};4(6(a.14)!="7"&&a.14!=""){q=(a.14)};4(6(a["1w"])!="7"&&a["1w"]!=""){c.2t=1D(a["1w"])};3 C="1N"1O 1k?1v:1u;3 D=o*d+"1h "+l;4(A!=""){D=A+" "+o*d+"1h "+l};3 E=o+"1h "+l;4(A!=""){E=A+" "+o+"1h "+l};c.1x=E;3 w=c.1f(a.1d).8;4(g==0){g=(w+r+s)};c.1S=m*d;3 F=c.1f("\u91cd").8;c.1y=n;j=g-(r+s);3 G=[];4(p==0){3 H="";3 I=0;3 J=1p 1q(/(<([^>]+)>)/,"2u");3 K=1p 1q(/<2v\\/>/,"g");a.1d=a.1d.1T(K,"");3 L=1p 1q(/<p[^>]*>(((?!<\\/p>)[\\s\\S])*)<\\/p>/,"g");1U(1V=L.2w(a.1d)){3 M="";3 N=1V[1];N=N.1T(J,"");3 O=N.1o("");3 P=0;1l(3 i=0;i<O.9;i++){3 Q=c.1f(O[i]).8;1B.1C(Q);3 R=P+Q;4(R>j){P=Q;G.1i(M);M=O[i]}W{M+=O[i];P+=Q}};4(M!=""){G.1i(M)};G.1i("");I++}4(G.9==0){3 M="";3 O=a.1d.1o("");3 P=0;1l(3 i=0;i<O.9;i++){3 Q=c.1f(O[i]).8;3 R=P+Q;4(R>j){P=Q;G.1i(M);M=O[i]}W{M+=O[i];P+=Q}};4(M!=""){G.1i(M)}}};c.1W();c.1j=q;c.1x=D;4(p==0&&G.9==0){3 S=(r+s);3 O=a.1d.1o("");1l(3 i=0;i<O.9;i++){3 R=S+F;4(R>j){p=i+1;2x}W{S=R;p=i+1}}};4(G.9==0){G=t.1X(a.1d,p)};3 T=10;3 h=0;1l(3 i=0;i<G.9;i++){4(G[i]!=""){h+=m}W{h+=T}};c.1Y();c.1W();f=h+v;b.8=g*d;b.1c=f*d;c.2y(0,0,b.8,b.1c);c.1j=q;c.1S=m*d;c.1x=D;c.1y=n;c.2z(0,0,b.8,b.1c);c.1y=k;e.y=o;3 U=(m-o);e.y+=U/2+u;e.y=e.y;1l(3 i=0;i<G.9;i++){3 N=G[i];3 x=e.x;3 y=e.y;4(q=="1G"){x=g-s}W 4(q=="1F"){x=g/2}W{x=x+r};x=x;y=y;4(c.1s){c.1s(N,x*d,y*d,z)}W{c.1t(N,x*d,y*d)};4(N==""){e.y+=T}W{e.y+=m}};c.1Y();c.2A();4(6(a.1Z)!="7"){b.1z.8=g+"1h";a.1Z.21(b)};4(B){b.1z.8=g+"1h";b.1z.1c="2B";B.21(b)};4(6(a.22)!="7"){a.22.2C(t,{1n:b})}},1X:15(a,n){3 b=[];3 c=0;1U(c<a.9){3 d=c+n;3 e=a.2D(c,d);b.1i(e);c=d};1e b}};1e V}',
62,164,"   var if  typeof undefined width length                                                 else color fontfamily lineheight  bgcolor  fontsize textalign function maxlength fontweight letterSpacing paddingleft paddingright paddingtop height text return measureText ctx px push textAlign document for this canvas split new RegExp left letterSpacingText fillText false true opacity font fillStyle style hccanvastxt console log parseFloat window center right GET_PIXEL_RATIO createElement getContext dpr bsr 100 ontouchend in id offsetWidth parseInt lineHeight replace while ite beginPath splitEqual closePath parent  appendChild success init 000000 opt rgba 10000   CanvasRenderingContext2D prototype getComputedStyle  forEach devicePixelRatio webkitBackingStorePixelRatio mozBackingStorePixelRatio msBackingStorePixelRatio oBackingStorePixelRatio backingStorePixelRatio CheckChinese u4E00 u9FFF test draw null getElementById parentNode globalAlpha ig br exec break clearRect fillRect save auto call substring".split(" "),
0,{}));