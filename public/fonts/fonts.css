/* SF Pro Display 系列 - 仅保留local()引用 */
@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 100;
    src: local('☺'), local('SF Pro Display Ultralight');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 200;
    src: local('☺'), local('SF Pro Display Thin');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 300;
    src: local('☺'), local('SF Pro Display Light');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 400;
    src: local('☺'), local('SF Pro Display Regular');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 500;
    src: local('☺'), local('SF Pro Display Medium');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 600;
    src: local('☺'), local('SF Pro Display Semibold');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 700;
    src: local('☺'), local('SF Pro Display Bold');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 800;
    src: local('☺'), local('SF Pro Display Heavy');
}

@font-face {
    font-family: 'SF Pro Display';
    font-style: normal;
    font-weight: 900;
    src: local('☺'), local('SF Pro Display Black');
}

/* SF Pro Text 系列 - 仅保留local()引用 */
@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 100;
    src: local('☺'), local('SF Pro Text Ultralight');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 200;
    src: local('☺'), local('SF Pro Text Thin');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 300;
    src: local('☺'), local('SF Pro Text Light');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 400;
    src: local('☺'), local('SF Pro Text Regular');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 500;
    src: local('☺'), local('SF Pro Text Medium');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 600;
    src: local('☺'), local('SF Pro Text Semibold');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 700;
    src: local('☺'), local('SF Pro Text Bold');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 800;
    src: local('☺'), local('SF Pro Text Heavy');
}

@font-face {
    font-family: 'SF Pro Text';
    font-style: normal;
    font-weight: 900;
    src: local('☺'), local('SF Pro Text Black');
}

/* SF Pro Icons 系列 - 仅保留local()引用 */
@font-face {
    font-family: 'SF Pro Icons';
    font-style: normal;
    font-weight: 300;
    src: local('☺'), local('SF Pro Icons Light');
}

@font-face {
    font-family: 'SF Pro Icons';
    font-style: normal;
    font-weight: 400;
    src: local('☺'), local('SF Pro Icons Regular');
}

@font-face {
    font-family: 'SF Pro Icons';
    font-style: normal;
    font-weight: 500;
    src: local('☺'), local('SF Pro Icons Medium');
}

@font-face {
    font-family: 'SF Pro Icons';
    font-style: normal;
    font-weight: 600;
    src: local('☺'), local('SF Pro Icons Semibold');
}

/* SF Pro SC (PingFang SC) 系列 - 仅保留local()引用 */
@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 100;
    src: local('☺'), local('PingFang SC Ultralight');
}

@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 200;
    src: local('☺'), local('PingFang SC Thin');
}

@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 300;
    src: local('☺'), local('PingFang SC Light');
}

@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 400;
    src: local('☺'), local('PingFang SC Regular');
}

@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 500;
    src: local('☺'), local('PingFang SC Medium');
}

@font-face {
    font-family: 'SF Pro SC';
    font-style: normal;
    font-weight: 600;
    src: local('☺'), local('PingFang SC Semibold');
}
