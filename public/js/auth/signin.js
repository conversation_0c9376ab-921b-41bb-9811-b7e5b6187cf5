/*! For license information please see signin.js.LICENSE.txt */
(()=>{function r(){var n,e,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.toStringTag||"@@toStringTag";function c(r,o,i,a){var c=o&&o.prototype instanceof p?o:p,f=Object.create(c.prototype);return t(f,"_invoke",function(r,t,o){var i,a,c,p=0,f=o||[],y=!1,s={p:0,n:0,v:n,a:l,f:l.bind(n,4),d:function(r,t){return i=r,a=0,c=n,s.n=t,u}};function l(r,t){for(a=r,c=t,e=0;!y&&p&&!o&&e<f.length;e++){var o,i=f[e],l=s.p,d=i[2];r>3?(o=d===t)&&(c=i[(a=i[4])?5:(a=3,3)],i[4]=i[5]=n):i[0]<=l&&((o=r<2&&l<i[1])?(a=0,s.v=t,s.n=i[1]):l<d&&(o=r<3||i[0]>t||t>d)&&(i[4]=r,i[5]=t,s.n=d,a=0))}if(o||r>1)return u;throw y=!0,t}return function(o,f,d){if(p>1)throw TypeError("Generator is already running");for(y&&1===f&&l(f,d),a=f,c=d;(e=a<2?n:c)||!y;){i||(a?a<3?(a>1&&(s.n=-1),l(a,c)):s.n=c:s.v=c);try{if(p=2,i){if(a||(o="next"),e=i[o]){if(!(e=e.call(i,c)))throw TypeError("iterator result is not an object");if(!e.done)return e;c=e.value,a<2&&(a=0)}else 1===a&&(e=i.return)&&e.call(i),a<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),a=1);i=n}else if((e=(y=s.n<0)?c:r.call(t,s))!==u)break}catch(r){i=n,a=1,c=r}finally{p=1}}return{value:e,done:y}}}(r,i,a),!0),f}var u={};function p(){}function f(){}function y(){}e=Object.getPrototypeOf;var s=[][i]?e(e([][i]())):(t(e={},i,function(){return this}),e),l=y.prototype=p.prototype=Object.create(s);function d(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,y):(r.__proto__=y,t(r,a,"GeneratorFunction")),r.prototype=Object.create(l),r}return f.prototype=y,t(l,"constructor",y),t(y,"constructor",f),f.displayName="GeneratorFunction",t(y,a,"GeneratorFunction"),t(l),t(l,a,"Generator"),t(l,i,function(){return this}),t(l,"toString",function(){return"[object Generator]"}),(r=function(){return{w:c,m:d}})()}function t(r,n,e,o){var i=Object.defineProperty;try{i({},"",{})}catch(r){i=0}t=function(r,n,e,o){if(n)i?i(r,n,{value:e,enumerable:!o,configurable:!o,writable:!o}):r[n]=e;else{var a=function(n,e){t(r,n,function(r){return this._invoke(n,e,r)})};a("next",0),a("throw",1),a("return",2)}},t(r,n,e,o)}function n(r,t,n,e,o,i,a){try{var c=r[i](a),u=c.value}catch(r){return void n(r)}c.done?t(u):Promise.resolve(u).then(e,o)}function e(){var t;return t=r().m(function t(){var n,e,o,i,a,c,u,p,f,y,s,l,d,v;return r().w(function(r){for(;;)switch(r.p=r.n){case 0:if(r.p=0,e=null===(n=document.getElementById("encryption-data"))||void 0===n?void 0:n.dataset.publicKey){r.n=1;break}throw new Error("客户端安全组件初始化失败。");case 1:return o=CryptoJS.lib.WordArray.random(32),i=CryptoJS.lib.WordArray.random(16),r.n=2,FingerprintJS.load();case 2:return a=r.v,r.n=3,a.get();case 3:if(c=r.v,u={visitorId:c.visitorId,components:c.components,timestamp:Date.now(),nonce:CryptoJS.lib.WordArray.random(16).toString(CryptoJS.enc.Hex)},p=JSON.stringify(u),f=CryptoJS.AES.encrypt(p,o,{iv:i,mode:CryptoJS.mode.CBC,padding:CryptoJS.pad.Pkcs7}).toString(),y={key:CryptoJS.enc.Base64.stringify(o),iv:CryptoJS.enc.Base64.stringify(i)},(s=new JSEncrypt).setPublicKey(e),l=s.encrypt(JSON.stringify(y))){r.n=4;break}throw new Error("客户端数据加密失败。");case 4:return d={key:l,data:f},r.a(2,JSON.stringify(d));case 5:throw r.p=5,v=r.v,console.error("Fingerprint encryption failed:",v),new Error("客户端安全检查失败，请刷新后重试。");case 6:return r.a(2)}},t,null,[[0,5]])}),e=function(){var r=this,e=arguments;return new Promise(function(o,i){var a=t.apply(r,e);function c(r){n(a,o,i,c,u,"next",r)}function u(r){n(a,o,i,c,u,"throw",r)}c(void 0)})},e.apply(this,arguments)}window.getEncryptedFingerprint=function(){return e.apply(this,arguments)}})();