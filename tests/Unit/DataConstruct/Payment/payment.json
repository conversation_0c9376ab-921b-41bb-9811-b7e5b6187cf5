{"primaryPaymentMethod": {"absoluteImagePath": "https://appleid.cdn-apple.com/static/bin/cb3345944805/images/wcpy.png", "absoluteImagePath2x": "https://appleid.cdn-apple.com/static/bin/cb179945678/images/<EMAIL>", "weChatPay": true, "partnerLogin": "xxxxxxxxxxx", "paymentMethodName": "WeChat Pay", "paymentMethodDetail": "xxxxxxxxxxx", "phoneNumber": {"number": "xxxxxxxxxxx", "numberWithoutAreaCode": "xxxxxxxxxxx", "sameAsAppleId": false, "northAmericaDisplayFormat": false, "rawNumber": "xxxxxxxxxxx", "fullNumberWithCountryPrefix": "xxxxxxxxxxx", "fullNumberWithoutCountryPrefix": "xxxxxxxxxxx", "countryCode": "86", "vetted": false, "countryDialCode": "86", "countryCodeAsString": "CN", "northAmericaPhone": false, "northAmericaDialCode": false, "loginHandle": false, "trusted": false, "type": "daytime"}, "ownerName": {"firstName": "语", "lastName": "蓝", "lastNameFirstOrderingRequired": false, "noSpaceRequiredInName": false, "fullPronounceName": "", "fullName": "语 蓝"}, "billingAddress": {"defaultAddress": false, "stateProvinceInvalid": true, "japanese": false, "korean": false, "formattedAddress": ["xxxxxxxxxxx", "xxxxxxxxxxx", "杭州", "445706 ZJ China mainland"], "primary": false, "shipping": false, "stateProvinces": [{"code": "AH", "name": "<PERSON><PERSON>"}, {"code": "AH", "name": "<PERSON><PERSON>"}, {"code": "BJ", "name": "Beijing"}, {"code": "BJ", "name": "Beijing"}, {"code": "CQ", "name": "Chongqing"}, {"code": "CQ", "name": "Chongqing"}, {"code": "FJ", "name": "Fujian"}, {"code": "FJ", "name": "Fujian"}, {"code": "GS", "name": "Gansu"}, {"code": "GS", "name": "Gansu"}, {"code": "GD", "name": "Guangdong"}, {"code": "GD", "name": "Guangdong"}, {"code": "GX", "name": "Guangxi"}, {"code": "GX", "name": "Guangxi"}, {"code": "GZ", "name": "Guizhou"}, {"code": "GZ", "name": "Guizhou"}, {"code": "HI", "name": "Hainan"}, {"code": "HI", "name": "Hainan"}, {"code": "HE", "name": "Hebei"}, {"code": "HE", "name": "Hebei"}, {"code": "HL", "name": "Heilongjiang"}, {"code": "HL", "name": "Heilongjiang"}, {"code": "HA", "name": "<PERSON><PERSON>"}, {"code": "HA", "name": "<PERSON><PERSON>"}, {"code": "HB", "name": "Hubei"}, {"code": "HB", "name": "Hubei"}, {"code": "HN", "name": "Hunan"}, {"code": "HN", "name": "Hunan"}, {"code": "JS", "name": "Jiangsu"}, {"code": "JS", "name": "Jiangsu"}, {"code": "JX", "name": "Jiangxi"}, {"code": "JX", "name": "Jiangxi"}, {"code": "JL", "name": "<PERSON><PERSON>"}, {"code": "JL", "name": "<PERSON><PERSON>"}, {"code": "LN", "name": "Liaoning"}, {"code": "LN", "name": "Liaoning"}, {"code": "NM", "name": "Inner Mongolia"}, {"code": "NM", "name": "Inner Mongolia"}, {"code": "NX", "name": "Ningxia"}, {"code": "NX", "name": "Ningxia"}, {"code": "QH", "name": "Qinghai"}, {"code": "QH", "name": "Qinghai"}, {"code": "SN", "name": "Shaanxi"}, {"code": "SN", "name": "Shaanxi"}, {"code": "SD", "name": "Shandong"}, {"code": "SD", "name": "Shandong"}, {"code": "SH", "name": "Shanghai"}, {"code": "SH", "name": "Shanghai"}, {"code": "SX", "name": "Shanxi"}, {"code": "SX", "name": "Shanxi"}, {"code": "SC", "name": "Sichuan"}, {"code": "SC", "name": "Sichuan"}, {"code": "TJ", "name": "Tianjin"}, {"code": "TJ", "name": "Tianjin"}, {"code": "XJ", "name": "Xinjiang"}, {"code": "XJ", "name": "Xinjiang"}, {"code": "XZ", "name": "Tibet"}, {"code": "XZ", "name": "Tibet"}, {"code": "YN", "name": "Yunnan"}, {"code": "YN", "name": "Yunnan"}, {"code": "ZJ", "name": "Zhejiang"}, {"code": "ZJ", "name": "Zhejiang"}], "stateProvinceCode": "ZJ", "stateProvince": {"code": "ZJ", "name": "Zhejiang"}, "countryCode": "CHN", "line1": "xxxxxxxxxxx", "line2": "xxxxxxxxxxx", "city": "杭州", "stateProvinceName": "Zhejiang", "postalCode": "445706", "countryName": "China mainland", "usa": false, "canada": false, "fullAddress": "xxxxxxxxxxx", "id": "1", "preferred": false}, "paymentAccountCountryCode": "CHN", "stackAware": false, "applePayPayment": false, "imagePath2x": "/images/<EMAIL>", "paymentSupported": true, "familyCard": false, "expirationSupported": false, "paymentMethodOption": {"name": "wechatpay"}, "none": false, "card": false, "carrierBilling": false, "alipay": false, "payPal": false, "ideal": false, "kakaoPay": false, "imageSupported": true, "appleCard": false, "appleCash": false, "applePay": false, "obfuscatedPhoneNumber": "+86-•••••••••74", "id": 1, "type": "wcpy", "deprecated": false, "imagePath": "/images/wcpy.png"}, "paymentMethodOptions": [{"option": {"name": "wechatpay", "displayName": "WeChat Pay"}}, {"option": {"name": "alipay", "displayName": "Alipay"}}, {"option": {"name": "card", "displayName": "Credit or Debit Card"}}, {"option": {"name": "none", "displayName": "None"}}], "currentPaymentOption": {"option": {"name": "wechatpay", "displayName": "WeChat Pay"}}, "addressFeatures": {"countryCode": "CHN", "zipCodeLabelRequired": false, "supportedCountries": [{"code": "AFG", "code2": "AF", "name": "Afghanistan", "dialCode": "93", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+93 (Afghanistan)"}, {"code": "ALB", "code2": "AL", "name": "Albania", "dialCode": "355", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+355 (Albania)"}, {"code": "DZA", "code2": "DZ", "name": "Algeria", "dialCode": "213", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 19, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+213 (Algeria)"}, {"code": "AGO", "code2": "AO", "name": "Angola", "dialCode": "244", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+244 (Angola)"}, {"code": "AIA", "code2": "AI", "name": "<PERSON><PERSON><PERSON>", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "AIA", "countryDialCode": "1", "countryCode2": "AI"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (<PERSON><PERSON><PERSON>)"}, {"code": "ATG", "code2": "AG", "name": "Antigua And Barbuda", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "ATG", "countryDialCode": "1", "countryCode2": "AG"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Antigua And Barbuda)"}, {"code": "ARG", "code2": "AR", "name": "Argentina", "dialCode": "54", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+54 (Argentina)"}, {"code": "ARM", "code2": "AM", "name": "Armenia", "dialCode": "374", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+374 (Armenia)"}, {"code": "AUS", "code2": "AU", "name": "Australia", "dialCode": "61", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+61 (Australia)"}, {"code": "AUT", "code2": "AT", "name": "Austria", "dialCode": "43", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+43 (Austria)"}, {"code": "AZE", "code2": "AZ", "name": "Azerbaijan", "dialCode": "994", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+994 (Azerbaijan)"}, {"code": "BHS", "code2": "BS", "name": "Bahamas", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "BHS", "countryDialCode": "1", "countryCode2": "BS"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Bahamas)"}, {"code": "BHR", "code2": "BH", "name": "Bahrain", "dialCode": "973", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+973 (Bahrain)"}, {"code": "BRB", "code2": "BB", "name": "Barbados", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "BRB", "countryDialCode": "1", "countryCode2": "BB"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Barbados)"}, {"code": "BLR", "code2": "BY", "name": "Belarus", "dialCode": "375", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+375 (Belarus)"}, {"code": "BEL", "code2": "BE", "name": "Belgium", "dialCode": "32", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+32 (Belgium)"}, {"code": "BLZ", "code2": "BZ", "name": "Belize", "dialCode": "501", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+501 (Belize)"}, {"code": "BEN", "code2": "BJ", "name": "Benin", "dialCode": "229", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+229 (Benin)"}, {"code": "BMU", "code2": "BM", "name": "Bermuda", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Bermuda)"}, {"code": "BTN", "code2": "BT", "name": "Bhutan", "dialCode": "975", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+975 (Bhutan)"}, {"code": "BOL", "code2": "BO", "name": "Bolivia", "dialCode": "591", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 16, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+591 (Bolivia)"}, {"code": "BIH", "code2": "BA", "name": "Bosnia and Herzegovina", "dialCode": "387", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+387 (Bosnia and Herzegovina)"}, {"code": "BWA", "code2": "BW", "name": "Botswana", "dialCode": "267", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+267 (Botswana)"}, {"code": "BRA", "code2": "BR", "name": "Brazil", "dialCode": "55", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+55 (Brazil)"}, {"code": "VGB", "code2": "VG", "name": "British Virgin Islands", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "VGB", "countryDialCode": "1", "countryCode2": "VG"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (British Virgin Islands)"}, {"code": "BRN", "code2": "BN", "name": "Brunei Darussalam", "dialCode": "673", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+673 (Brunei Darussalam)"}, {"code": "BGR", "code2": "BG", "name": "Bulgaria", "dialCode": "359", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+359 (Bulgaria)"}, {"code": "BFA", "code2": "BF", "name": "Burkina Faso", "dialCode": "226", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 20, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+226 (Burkina Faso)"}, {"code": "KHM", "code2": "KH", "name": "Cambodia", "dialCode": "855", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+855 (Cambodia)"}, {"code": "CMR", "code2": "CM", "name": "Cameroon", "dialCode": "237", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+237 (Cameroon)"}, {"code": "CAN", "code2": "CA", "name": "Canada", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 19, "supportHSA2": true, "phoneNumberFormat": {"format": "(xxx) xxx-xxxx", "formatDigitRegions": [1, 4, 6, 9, 10, 14], "minDigitLength": 10, "maxDigitLength": 10, "countryCode": "CAN", "countryDialCode": "1", "countryCode2": "CA"}, "supportPaidAccount": false, "stateProvinces": [{"code": "AB", "name": "Alberta"}, {"code": "BC", "name": "British Columbia"}, {"code": "MB", "name": "Manitoba"}, {"code": "NB", "name": "New Brunswick"}, {"code": "NF", "name": "Newfoundland & Labrador"}, {"code": "NS", "name": "Nova Scotia"}, {"code": "NT", "name": "Northwest Territories"}, {"code": "NU", "name": "Nunavut"}, {"code": "ON", "name": "Ontario"}, {"code": "PE", "name": "Prince Edward Island"}, {"code": "QC", "name": "Quebec"}, {"code": "SK", "name": "Saskatchewan"}, {"code": "YT", "name": "Yukon"}], "japan": false, "korea": false, "usa": false, "canada": true, "uk": false, "dialCodeDisplay": "+1 (Canada)"}, {"code": "CPV", "code2": "CV", "name": "Cape Verde", "dialCode": "238", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+238 (Cape Verde)"}, {"code": "CYM", "code2": "KY", "name": "Cayman Islands", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "CYM", "countryDialCode": "1", "countryCode2": "KY"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Cayman Islands)"}, {"code": "TCD", "code2": "TD", "name": "Chad", "dialCode": "235", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+235 (Chad)"}, {"code": "CHL", "code2": "CL", "name": "Chile", "dialCode": "56", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+56 (Chile)"}, {"code": "CHN", "code2": "CN", "name": "China mainland", "dialCode": "86", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxxx xxxx", "formatDigitRegions": [0, 3, 4, 8, 9, 13], "minDigitLength": 1, "maxDigitLength": 11, "countryCode": "CHN", "countryDialCode": "86", "countryCode2": "CN"}, "supportPaidAccount": true, "stateProvinces": [{"code": "110", "name": "<PERSON><PERSON>"}, {"code": "10", "name": "Beijing"}, {"code": "11", "name": "Chongqing"}, {"code": "150", "name": "Fujian"}, {"code": "260", "name": "Gansu"}, {"code": "190", "name": "Guangdong"}, {"code": "210", "name": "Guangxi"}, {"code": "220", "name": "Guizhou"}, {"code": "200", "name": "Hainan"}, {"code": "60", "name": "Hebei"}, {"code": "90", "name": "Heilongjiang"}, {"code": "180", "name": "<PERSON><PERSON>"}, {"code": "170", "name": "Hubei"}, {"code": "160", "name": "Hunan"}, {"code": "100", "name": "Jiangsu"}, {"code": "140", "name": "Jiangxi"}, {"code": "80", "name": "<PERSON><PERSON>"}, {"code": "70", "name": "Liaoning"}, {"code": "40", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"code": "270", "name": "Ningxia"}, {"code": "280", "name": "Qinghai"}, {"code": "250", "name": "Shaanxi"}, {"code": "120", "name": "Shandong"}, {"code": "20", "name": "Shanghai"}, {"code": "50", "name": "Shanxi"}, {"code": "230", "name": "Sichuan"}, {"code": "30", "name": "Tianjin"}, {"code": "290", "name": "Xinjiang"}, {"code": "300", "name": "Xizang"}, {"code": "240", "name": "Yunnan"}, {"code": "130", "name": "Zhejiang"}], "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+86 (China mainland)"}, {"code": "COL", "code2": "CO", "name": "Colombia", "dialCode": "57", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+57 (Colombia)"}, {"code": "CRI", "code2": "CR", "name": "Costa Rica", "dialCode": "506", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+506 (Costa Rica)"}, {"code": "HRV", "code2": "HR", "name": "Croatia", "dialCode": "385", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+385 (Croatia)"}, {"code": "CYP", "code2": "CY", "name": "Cyprus", "dialCode": "357", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+357 (Cyprus)"}, {"code": "CZE", "code2": "CZ", "name": "Czechia", "dialCode": "420", "embargoed": false, "underAgeLimit": 15, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxx xxx", "formatDigitRegions": [0, 3, 4, 7, 8, 11], "minDigitLength": 9, "maxDigitLength": 9, "countryCode": "CZE", "countryDialCode": "420", "countryCode2": "CZ"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+420 (Czechia)"}, {"code": "CIV", "code2": "CI", "name": "Côte d'Ivoire", "dialCode": "225", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+225 (Côte d'Ivoire)"}, {"code": "COD", "code2": "CD", "name": "Democratic Republic of the Congo", "dialCode": "243", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+243 (Democratic Republic of the Congo)"}, {"code": "DNK", "code2": "DK", "name": "Denmark", "dialCode": "45", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+45 (Denmark)"}, {"code": "DMA", "code2": "DM", "name": "Dominica", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "DMA", "countryDialCode": "1", "countryCode2": "DM"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (<PERSON><PERSON>)"}, {"code": "DOM", "code2": "DO", "name": "Dominican Republic", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Dominican Republic)"}, {"code": "ECU", "code2": "EC", "name": "Ecuador", "dialCode": "593", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+593 (Ecuador)"}, {"code": "EGY", "code2": "EG", "name": "Egypt", "dialCode": "20", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+20 (Egypt)"}, {"code": "SLV", "code2": "SV", "name": "El Salvador", "dialCode": "503", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+503 (El Salvador)"}, {"code": "EST", "code2": "EE", "name": "Estonia", "dialCode": "372", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+372 (Estonia)"}, {"code": "SWZ", "code2": "SZ", "name": "<PERSON><PERSON><PERSON><PERSON>", "dialCode": "268", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+268 (<PERSON><PERSON><PERSON><PERSON>)"}, {"code": "FJI", "code2": "FJ", "name": "Fiji", "dialCode": "679", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+679 (Fiji)"}, {"code": "FIN", "code2": "FI", "name": "Finland", "dialCode": "358", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+358 (Finland)"}, {"code": "FRA", "code2": "FR", "name": "France", "dialCode": "33", "embargoed": false, "underAgeLimit": 15, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xx xx xx xx xx", "formatDigitRegions": [0, 2, 3, 5, 6, 8, 9, 11, 12, 14], "minDigitLength": 1, "maxDigitLength": 10, "countryCode": "FRA", "countryDialCode": "33", "countryCode2": "FR"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+33 (France)"}, {"code": "GAB", "code2": "GA", "name": "Gabon", "dialCode": "241", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+241 (Gabon)"}, {"code": "GMB", "code2": "GM", "name": "Gambia", "dialCode": "220", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+220 (Gambia)"}, {"code": "GEO", "code2": "GE", "name": "Georgia", "dialCode": "995", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+995 (Georgia)"}, {"code": "DEU", "code2": "DE", "name": "Germany", "dialCode": "49", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+49 (Germany)"}, {"code": "GHA", "code2": "GH", "name": "Ghana", "dialCode": "233", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+233 (Ghana)"}, {"code": "GRC", "code2": "GR", "name": "Greece", "dialCode": "30", "embargoed": false, "underAgeLimit": 15, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxx xxxx", "formatDigitRegions": [0, 3, 4, 7, 8, 12], "minDigitLength": 10, "maxDigitLength": 10, "countryCode": "GRC", "countryDialCode": "30", "countryCode2": "GR"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+30 (Greece)"}, {"code": "GRD", "code2": "GD", "name": "Grenada", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "GRD", "countryDialCode": "1", "countryCode2": "GD"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Grenada)"}, {"code": "GTM", "code2": "GT", "name": "Guatemala", "dialCode": "502", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+502 (Guatemala)"}, {"code": "GNB", "code2": "GW", "name": "Guinea-Bissau", "dialCode": "245", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+245 (Guinea-Bissau)"}, {"code": "GUY", "code2": "GY", "name": "Guyana", "dialCode": "592", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+592 (Guyana)"}, {"code": "HND", "code2": "HN", "name": "Honduras", "dialCode": "504", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+504 (Honduras)"}, {"code": "HKG", "code2": "HK", "name": "Hong Kong", "dialCode": "852", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+852 (Hong Kong)"}, {"code": "HUN", "code2": "HU", "name": "Hungary", "dialCode": "36", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+36 (Hungary)"}, {"code": "ISL", "code2": "IS", "name": "Iceland", "dialCode": "354", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+354 (Iceland)"}, {"code": "IND", "code2": "IN", "name": "India", "dialCode": "91", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxx xxxx", "formatDigitRegions": [0, 3, 4, 7, 8, 12], "minDigitLength": 10, "maxDigitLength": 10, "countryCode": "IND", "countryDialCode": "91", "countryCode2": "IN"}, "supportPaidAccount": true, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+91 (India)"}, {"code": "IDN", "code2": "ID", "name": "Indonesia", "dialCode": "62", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+62 (Indonesia)"}, {"code": "IRQ", "code2": "IQ", "name": "Iraq", "dialCode": "964", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+964 (Iraq)"}, {"code": "IRL", "code2": "IE", "name": "Ireland", "dialCode": "353", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+353 (Ireland)"}, {"code": "ISR", "code2": "IL", "name": "Israel", "dialCode": "972", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+972 (Israel)"}, {"code": "ITA", "code2": "IT", "name": "Italy", "dialCode": "39", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+39 (Italy)"}, {"code": "JAM", "code2": "JM", "name": "Jamaica", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "JAM", "countryDialCode": "1", "countryCode2": "JM"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Jamaica)"}, {"code": "JPN", "code2": "JP", "name": "Japan", "dialCode": "81", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "stateProvinces": [{"code": "1", "name": "Hokkaido"}, {"code": "2", "name": "<PERSON><PERSON><PERSON>"}, {"code": "3", "name": "Iwate"}, {"code": "4", "name": "<PERSON><PERSON><PERSON>"}, {"code": "5", "name": "<PERSON><PERSON><PERSON>"}, {"code": "6", "name": "Yamagata"}, {"code": "7", "name": "Fukushima"}, {"code": "48", "name": "<PERSON><PERSON><PERSON>"}, {"code": "9", "name": "Tochigi"}, {"code": "10", "name": "<PERSON><PERSON>"}, {"code": "11", "name": "<PERSON><PERSON><PERSON>"}, {"code": "12", "name": "Chiba"}, {"code": "13", "name": "Tokyo"}, {"code": "14", "name": "Kanagawa"}, {"code": "15", "name": "Niigata"}, {"code": "16", "name": "Toyama"}, {"code": "17", "name": "<PERSON><PERSON><PERSON>"}, {"code": "18", "name": "<PERSON><PERSON><PERSON>"}, {"code": "19", "name": "<PERSON><PERSON><PERSON>"}, {"code": "50", "name": "Nagano"}, {"code": "21", "name": "Gifu"}, {"code": "22", "name": "Shizuoka"}, {"code": "23", "name": "<PERSON><PERSON>"}, {"code": "24", "name": "<PERSON><PERSON>"}, {"code": "25", "name": "Shiga"}, {"code": "26", "name": "Kyoto"}, {"code": "27", "name": "Osaka"}, {"code": "28", "name": "Hyogo"}, {"code": "29", "name": "<PERSON><PERSON>"}, {"code": "30", "name": "<PERSON><PERSON><PERSON>"}, {"code": "31", "name": "Tottori"}, {"code": "32", "name": "<PERSON><PERSON><PERSON>"}, {"code": "33", "name": "<PERSON><PERSON>"}, {"code": "34", "name": "Hiroshima"}, {"code": "35", "name": "<PERSON><PERSON><PERSON>"}, {"code": "36", "name": "Tokushima"}, {"code": "37", "name": "Kagawa"}, {"code": "38", "name": "<PERSON><PERSON><PERSON>"}, {"code": "39", "name": "<PERSON><PERSON>"}, {"code": "40", "name": "<PERSON><PERSON><PERSON>"}, {"code": "41", "name": "Saga"}, {"code": "42", "name": "Nagasaki"}, {"code": "43", "name": "<PERSON><PERSON>"}, {"code": "44", "name": "<PERSON><PERSON>"}, {"code": "45", "name": "<PERSON><PERSON><PERSON>"}, {"code": "46", "name": "Kagoshima"}, {"code": "47", "name": "Okinawa"}], "japan": true, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+81 (Japan)"}, {"code": "JOR", "code2": "JO", "name": "Jordan", "dialCode": "962", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+962 (Jordan)"}, {"code": "KAZ", "code2": "KZ", "name": "Kazakhstan", "dialCode": "7", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+7 (Kazakhstan)"}, {"code": "KEN", "code2": "KE", "name": "Kenya", "dialCode": "254", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+254 (Kenya)"}, {"code": "XKS", "code2": "XK", "name": "Kosovo", "dialCode": "383", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+383 (Kosovo)"}, {"code": "KWT", "code2": "KW", "name": "Kuwait", "dialCode": "965", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+965 (Kuwait)"}, {"code": "KGZ", "code2": "KG", "name": "Kyrgyzstan", "dialCode": "996", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+996 (Kyrgyzstan)"}, {"code": "LAO", "code2": "LA", "name": "Laos", "dialCode": "856", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+856 (Laos)"}, {"code": "LVA", "code2": "LV", "name": "Latvia", "dialCode": "371", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+371 (Latvia)"}, {"code": "LBN", "code2": "LB", "name": "Lebanon", "dialCode": "961", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+961 (Lebanon)"}, {"code": "LBR", "code2": "LR", "name": "Liberia", "dialCode": "231", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+231 (Liberia)"}, {"code": "LBY", "code2": "LY", "name": "Libya", "dialCode": "218", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+218 (Libya)"}, {"code": "LTU", "code2": "LT", "name": "Lithuania", "dialCode": "370", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+370 (Lithuania)"}, {"code": "LUX", "code2": "LU", "name": "Luxembourg", "dialCode": "352", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+352 (Luxembourg)"}, {"code": "MAC", "code2": "MO", "name": "Macao", "dialCode": "853", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+853 (<PERSON><PERSON>)"}, {"code": "MDG", "code2": "MG", "name": "Madagascar", "dialCode": "261", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+261 (Madagascar)"}, {"code": "MWI", "code2": "MW", "name": "Malawi", "dialCode": "265", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+265 (Malawi)"}, {"code": "MYS", "code2": "MY", "name": "Malaysia", "dialCode": "60", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+60 (Malaysia)"}, {"code": "MDV", "code2": "MV", "name": "Maldives", "dialCode": "960", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+960 (Maldives)"}, {"code": "MLI", "code2": "ML", "name": "Mali", "dialCode": "223", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+223 (Mali)"}, {"code": "MLT", "code2": "MT", "name": "Malta", "dialCode": "356", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+356 (Malta)"}, {"code": "MRT", "code2": "MR", "name": "Mauritania", "dialCode": "222", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+222 (Mauritania)"}, {"code": "MUS", "code2": "MU", "name": "Mauritius", "dialCode": "230", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+230 (Mauritius)"}, {"code": "MEX", "code2": "MX", "name": "Mexico", "dialCode": "52", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+52 (Mexico)"}, {"code": "FSM", "code2": "FM", "name": "Micronesia", "dialCode": "691", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+691 (Micronesia)"}, {"code": "MDA", "code2": "MD", "name": "Moldova", "dialCode": "373", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+373 (Moldova)"}, {"code": "MNG", "code2": "MN", "name": "Mongolia", "dialCode": "976", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+976 (Mongolia)"}, {"code": "MNE", "code2": "ME", "name": "Montenegro", "dialCode": "382", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+382 (Montenegro)"}, {"code": "MSR", "code2": "MS", "name": "Montserrat", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (<PERSON><PERSON><PERSON>)"}, {"code": "MAR", "code2": "MA", "name": "Morocco", "dialCode": "212", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+212 (Morocco)"}, {"code": "MOZ", "code2": "MZ", "name": "Mozambique", "dialCode": "258", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+258 (Mozambique)"}, {"code": "MMR", "code2": "MM", "name": "Myanmar", "dialCode": "95", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+95 (Myanmar)"}, {"code": "NAM", "code2": "NA", "name": "Namibia", "dialCode": "264", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+264 (Namibia)"}, {"code": "NRU", "code2": "NR", "name": "Nauru", "dialCode": "674", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+674 (Nauru)"}, {"code": "NPL", "code2": "NP", "name": "Nepal", "dialCode": "977", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+977 (Nepal)"}, {"code": "NLD", "code2": "NL", "name": "Netherlands", "dialCode": "31", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+31 (Netherlands)"}, {"code": "NZL", "code2": "NZ", "name": "New Zealand", "dialCode": "64", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+64 (New Zealand)"}, {"code": "NIC", "code2": "NI", "name": "Nicaragua", "dialCode": "505", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+505 (Nicaragua)"}, {"code": "NER", "code2": "NE", "name": "Niger", "dialCode": "227", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+227 (Niger)"}, {"code": "NGA", "code2": "NG", "name": "Nigeria", "dialCode": "234", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+234 (Nigeria)"}, {"code": "MKD", "code2": "MK", "name": "North Macedonia", "dialCode": "389", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+389 (North Macedonia)"}, {"code": "NOR", "code2": "NO", "name": "Norway", "dialCode": "47", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+47 (Norway)"}, {"code": "OMN", "code2": "OM", "name": "Oman", "dialCode": "968", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+968 (Oman)"}, {"code": "PAK", "code2": "PK", "name": "Pakistan", "dialCode": "92", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+92 (Pakistan)"}, {"code": "PLW", "code2": "PW", "name": "<PERSON><PERSON>", "dialCode": "680", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+680 (Palau)"}, {"code": "PAN", "code2": "PA", "name": "Panama", "dialCode": "507", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+507 (Panama)"}, {"code": "PNG", "code2": "PG", "name": "Papua New Guinea", "dialCode": "675", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+675 (Papua New Guinea)"}, {"code": "PRY", "code2": "PY", "name": "Paraguay", "dialCode": "595", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+595 (Paraguay)"}, {"code": "PER", "code2": "PE", "name": "Peru", "dialCode": "51", "embargoed": false, "underAgeLimit": 15, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+51 (Peru)"}, {"code": "PHL", "code2": "PH", "name": "Philippines", "dialCode": "63", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+63 (Philippines)"}, {"code": "POL", "code2": "PL", "name": "Poland", "dialCode": "48", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+48 (Poland)"}, {"code": "PRT", "code2": "PT", "name": "Portugal", "dialCode": "351", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+351 (Portugal)"}, {"code": "QAT", "code2": "QA", "name": "Qatar", "dialCode": "974", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+974 (Qatar)"}, {"code": "COG", "code2": "CG", "name": "Republic of the Congo", "dialCode": "242", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+242 (Republic of the Congo)"}, {"code": "ROU", "code2": "RO", "name": "Romania", "dialCode": "40", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+40 (Romania)"}, {"code": "RUS", "code2": "RU", "name": "Russia", "dialCode": "7", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+7 (Russia)"}, {"code": "RWA", "code2": "RW", "name": "Rwanda", "dialCode": "250", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+250 (Rwanda)"}, {"code": "KNA", "code2": "KN", "name": "Saint Kitts And Nevis", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "KNA", "countryDialCode": "1", "countryCode2": "KN"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Saint Kitts And Nevis)"}, {"code": "LCA", "code2": "LC", "name": "Saint Lucia", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "LCA", "countryDialCode": "1", "countryCode2": "LC"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Saint Lucia)"}, {"code": "VCT", "code2": "VC", "name": "Saint Vincent and the Grenadines", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "VCT", "countryDialCode": "1", "countryCode2": "VC"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Saint <PERSON> and the Grenadines)"}, {"code": "STP", "code2": "ST", "name": "Sao Tome And Principe", "dialCode": "239", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+239 (Sao Tome And Principe)"}, {"code": "SAU", "code2": "SA", "name": "Saudi Arabia", "dialCode": "966", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxxxxxxxx", "formatDigitRegions": [0, 9], "minDigitLength": 9, "maxDigitLength": 9, "countryCode": "SAU", "countryDialCode": "966", "countryCode2": "SA"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+966 (Saudi Arabia)"}, {"code": "SEN", "code2": "SN", "name": "Senegal", "dialCode": "221", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+221 (Senegal)"}, {"code": "SRB", "code2": "RS", "name": "Serbia", "dialCode": "381", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+381 (Serbia)"}, {"code": "SYC", "code2": "SC", "name": "Seychelles", "dialCode": "248", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+248 (Seychelles)"}, {"code": "SLE", "code2": "SL", "name": "Sierra Leone", "dialCode": "232", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+232 (Sierra Leone)"}, {"code": "SGP", "code2": "SG", "name": "Singapore", "dialCode": "65", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+65 (Singapore)"}, {"code": "SVK", "code2": "SK", "name": "Slovakia", "dialCode": "421", "embargoed": false, "underAgeLimit": 16, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+421 (Slovakia)"}, {"code": "SVN", "code2": "SI", "name": "Slovenia", "dialCode": "386", "embargoed": false, "underAgeLimit": 15, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+386 (Slovenia)"}, {"code": "SLB", "code2": "SB", "name": "Solomon Islands", "dialCode": "677", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+677 (Solomon Islands)"}, {"code": "ZAF", "code2": "ZA", "name": "South Africa", "dialCode": "27", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+27 (South Africa)"}, {"code": "KOR", "code2": "KR", "name": "South Korea", "dialCode": "82", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 19, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": true, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+82 (South Korea)"}, {"code": "ESP", "code2": "ES", "name": "Spain", "dialCode": "34", "embargoed": false, "underAgeLimit": 14, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxx xxx", "formatDigitRegions": [0, 3, 4, 7, 8, 11], "minDigitLength": 9, "maxDigitLength": 9, "countryCode": "ESP", "countryDialCode": "34", "countryCode2": "ES"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+34 (Spain)"}, {"code": "LKA", "code2": "LK", "name": "Sri Lanka", "dialCode": "94", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+94 (Sri Lanka)"}, {"code": "SUR", "code2": "SR", "name": "Suriname", "dialCode": "597", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+597 (Suriname)"}, {"code": "SWE", "code2": "SE", "name": "Sweden", "dialCode": "46", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+46 (Sweden)"}, {"code": "CHE", "code2": "CH", "name": "Switzerland", "dialCode": "41", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx xxx xx xx", "formatDigitRegions": [0, 3, 4, 7, 8, 10, 11, 13], "minDigitLength": 1, "maxDigitLength": 10, "countryCode": "CHE", "countryDialCode": "41", "countryCode2": "CH"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+41 (Switzerland)"}, {"code": "TWN", "code2": "TW", "name": "Taiwan", "dialCode": "886", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+886 (Taiwan)"}, {"code": "TJK", "code2": "TJ", "name": "Tajikistan", "dialCode": "992", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+992 (Tajikistan)"}, {"code": "TZA", "code2": "TZ", "name": "Tanzania", "dialCode": "255", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+255 (Tanzania)"}, {"code": "THA", "code2": "TH", "name": "Thailand", "dialCode": "66", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 20, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+66 (Thailand)"}, {"code": "TON", "code2": "TO", "name": "Tonga", "dialCode": "676", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+676 (Tonga)"}, {"code": "TTO", "code2": "TT", "name": "Trinidad and Tobago", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "TTO", "countryDialCode": "1", "countryCode2": "TT"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Trinidad and Tobago)"}, {"code": "TUN", "code2": "TN", "name": "Tunisia", "dialCode": "216", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+216 (Tunisia)"}, {"code": "TKM", "code2": "TM", "name": "Turkmenistan", "dialCode": "993", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+993 (Turkmenistan)"}, {"code": "TCA", "code2": "TC", "name": "Turks and Caicos Islands", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "xxx-xxxx", "formatDigitRegions": [0, 3, 4, 8], "minDigitLength": 7, "maxDigitLength": 7, "countryCode": "TCA", "countryDialCode": "1", "countryCode2": "TC"}, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+1 (Turks and Caicos Islands)"}, {"code": "TUR", "code2": "TR", "name": "Türkiye", "dialCode": "90", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+90 (<PERSON><PERSON><PERSON><PERSON><PERSON>)"}, {"code": "UGA", "code2": "UG", "name": "Uganda", "dialCode": "256", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+256 (Uganda)"}, {"code": "UKR", "code2": "UA", "name": "Ukraine", "dialCode": "380", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+380 (Ukraine)"}, {"code": "ARE", "code2": "AE", "name": "United Arab Emirates", "dialCode": "971", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 21, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+971 (United Arab Emirates)"}, {"code": "GBR", "code2": "GB", "name": "United Kingdom", "dialCode": "44", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": true, "dialCodeDisplay": "+44 (United Kingdom)"}, {"code": "USA", "code2": "US", "name": "United States", "dialCode": "1", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "phoneNumberFormat": {"format": "(xxx) xxx-xxxx", "formatDigitRegions": [1, 4, 6, 9, 10, 14], "minDigitLength": 10, "maxDigitLength": 10, "countryCode": "USA", "countryDialCode": "1", "countryCode2": "US"}, "supportPaidAccount": false, "stateProvinces": [{"code": "AL", "name": "Alabama"}, {"code": "AK", "name": "Alaska"}, {"code": "AZ", "name": "Arizona"}, {"code": "AR", "name": "Arkansas"}, {"code": "AA", "name": "Armed Forces Americas"}, {"code": "AE", "name": "Armed Forces Europe"}, {"code": "AP", "name": "Armed Forces Pacific"}, {"code": "CA", "name": "California"}, {"code": "CO", "name": "Colorado"}, {"code": "CT", "name": "Connecticut"}, {"code": "DC", "name": "Dist Of Columbia"}, {"code": "DE", "name": "Delaware"}, {"code": "FL", "name": "Florida"}, {"code": "GA", "name": "Georgia"}, {"code": "GU", "name": "Guam"}, {"code": "HI", "name": "Hawaii"}, {"code": "IA", "name": "Iowa"}, {"code": "ID", "name": "Idaho"}, {"code": "IL", "name": "Illinois"}, {"code": "IN", "name": "Indiana"}, {"code": "KS", "name": "Kansas"}, {"code": "KY", "name": "Kentucky"}, {"code": "LA", "name": "Louisiana"}, {"code": "MA", "name": "Massachusetts"}, {"code": "MD", "name": "Maryland"}, {"code": "ME", "name": "Maine"}, {"code": "MI", "name": "Michigan"}, {"code": "MN", "name": "Minnesota"}, {"code": "MO", "name": "Missouri"}, {"code": "MS", "name": "Mississippi"}, {"code": "MT", "name": "Montana"}, {"code": "NC", "name": "North Carolina"}, {"code": "ND", "name": "North Dakota"}, {"code": "NE", "name": "Nebraska"}, {"code": "NH", "name": "New Hampshire"}, {"code": "NJ", "name": "New Jersey"}, {"code": "NM", "name": "New Mexico"}, {"code": "NV", "name": "Nevada"}, {"code": "NY", "name": "New York"}, {"code": "OH", "name": "Ohio"}, {"code": "OK", "name": "Oklahoma"}, {"code": "OR", "name": "Oregon"}, {"code": "PA", "name": "Pennsylvania"}, {"code": "PR", "name": "Puerto Rico"}, {"code": "RI", "name": "Rhode Island"}, {"code": "SC", "name": "South Carolina"}, {"code": "SD", "name": "South Dakota"}, {"code": "TN", "name": "Tennessee"}, {"code": "TX", "name": "Texas"}, {"code": "UT", "name": "Utah"}, {"code": "VA", "name": "Virginia"}, {"code": "VI", "name": "Virgin Islands"}, {"code": "VT", "name": "Vermont"}, {"code": "WA", "name": "Washington"}, {"code": "WI", "name": "Wisconsin"}, {"code": "WV", "name": "West Virginia"}, {"code": "WY", "name": "Wyoming"}], "japan": false, "korea": false, "usa": true, "canada": false, "uk": false, "dialCodeDisplay": "+1 (United States)"}, {"code": "URY", "code2": "UY", "name": "Uruguay", "dialCode": "598", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+598 (Uruguay)"}, {"code": "UZB", "code2": "UZ", "name": "Uzbekistan", "dialCode": "998", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+998 (Uzbekistan)"}, {"code": "VUT", "code2": "VU", "name": "Vanuatu", "dialCode": "678", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+678 (Vanuatu)"}, {"code": "VEN", "code2": "VE", "name": "Venezuela", "dialCode": "58", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+58 (Venezuela)"}, {"code": "VNM", "code2": "VN", "name": "Vietnam", "dialCode": "84", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+84 (Vietnam)"}, {"code": "YEM", "code2": "YE", "name": "Yemen", "dialCode": "967", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+967 (Yemen)"}, {"code": "ZMB", "code2": "ZM", "name": "Zambia", "dialCode": "260", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+260 (Zambia)"}, {"code": "ZWE", "code2": "ZW", "name": "Zimbabwe", "dialCode": "263", "embargoed": false, "underAgeLimit": 13, "minorAgeLimit": 18, "supportHSA2": true, "supportPaidAccount": false, "japan": false, "korea": false, "usa": false, "canada": false, "uk": false, "dialCodeDisplay": "+263 (Zimbabwe)"}], "stateProvinceOptions": [{"code": "AH", "name": "<PERSON><PERSON>"}, {"code": "BJ", "name": "Beijing"}, {"code": "CQ", "name": "Chongqing"}, {"code": "FJ", "name": "Fujian"}, {"code": "GS", "name": "Gansu"}, {"code": "GD", "name": "Guangdong"}, {"code": "GX", "name": "Guangxi"}, {"code": "GZ", "name": "Guizhou"}, {"code": "HI", "name": "Hainan"}, {"code": "HE", "name": "Hebei"}, {"code": "HL", "name": "Heilongjiang"}, {"code": "HA", "name": "<PERSON><PERSON>"}, {"code": "HB", "name": "Hubei"}, {"code": "HN", "name": "Hunan"}, {"code": "JS", "name": "Jiangsu"}, {"code": "JX", "name": "Jiangxi"}, {"code": "JL", "name": "<PERSON><PERSON>"}, {"code": "LN", "name": "Liaoning"}, {"code": "NM", "name": "Inner Mongolia"}, {"code": "NX", "name": "Ningxia"}, {"code": "QH", "name": "Qinghai"}, {"code": "SN", "name": "Shaanxi"}, {"code": "SD", "name": "Shandong"}, {"code": "SH", "name": "Shanghai"}, {"code": "SX", "name": "Shanxi"}, {"code": "SC", "name": "Sichuan"}, {"code": "TJ", "name": "Tianjin"}, {"code": "XJ", "name": "Xinjiang"}, {"code": "XZ", "name": "Tibet"}, {"code": "YN", "name": "Yunnan"}, {"code": "ZJ", "name": "Zhejiang"}], "allowChangeCountry": true, "accountFeatures": {"isADPEnabled": false}, "unionPaySupported": true}, "addressFormat": {"fields": {"firstName": {"id": "firstName", "type": "text", "required": true, "title": "Given Name", "placeholder": "Required"}, "lastName": {"id": "lastName", "type": "text", "required": true, "title": "Last Name", "placeholder": "Required"}, "line1": {"id": "line1", "type": "text", "required": true, "title": "Street", "placeholder": "Required"}, "line2": {"id": "line2", "type": "text", "required": false, "title": "Street", "placeholder": "Optional"}, "line3": {"id": "line3", "type": "text", "required": false, "title": "Street", "placeholder": "Optional"}, "city": {"id": "city", "type": "text", "required": true, "title": "Region", "placeholder": "City"}, "stateProvince": {"id": "stateProvince", "type": "dropdown", "required": true, "title": "Select", "placeholder": "Select", "values": [{"name": "AH", "title": "<PERSON><PERSON>"}, {"name": "BJ", "title": "Beijing"}, {"name": "CQ", "title": "Chongqing"}, {"name": "FJ", "title": "Fujian"}, {"name": "GS", "title": "Gansu"}, {"name": "GD", "title": "Guangdong"}, {"name": "GX", "title": "Guangxi"}, {"name": "GZ", "title": "Guizhou"}, {"name": "HI", "title": "Hainan"}, {"name": "HE", "title": "Hebei"}, {"name": "HL", "title": "Heilongjiang"}, {"name": "HA", "title": "<PERSON><PERSON>"}, {"name": "HB", "title": "Hubei"}, {"name": "HN", "title": "Hunan"}, {"name": "JS", "title": "Jiangsu"}, {"name": "JX", "title": "Jiangxi"}, {"name": "JL", "title": "<PERSON><PERSON>"}, {"name": "LN", "title": "Liaoning"}, {"name": "NM", "title": "Inner Mongolia"}, {"name": "NX", "title": "Ningxia"}, {"name": "QH", "title": "Qinghai"}, {"name": "SN", "title": "Shaanxi"}, {"name": "SD", "title": "Shandong"}, {"name": "SH", "title": "Shanghai"}, {"name": "SX", "title": "Shanxi"}, {"name": "SC", "title": "Sichuan"}, {"name": "TJ", "title": "Tianjin"}, {"name": "XJ", "title": "Xinjiang"}, {"name": "XZ", "title": "Tibet"}, {"name": "YN", "title": "Yunnan"}, {"name": "ZJ", "title": "Zhejiang"}]}, "postalCode": {"id": "postalCode", "type": "text", "required": true, "title": "Postal Code", "placeholder": "ZIP"}, "country": {"id": "country", "type": "text", "required": true, "title": "Country/Region"}, "phoneCountryDialCode": {"id": "phoneCountryDialCode", "type": "text", "required": false, "title": "Phone", "placeholder": "Phone"}, "phoneNumber": {"id": "phoneNumber", "type": "text", "required": true, "title": "Phone", "placeholder": "Phone"}, "paymentType": {"id": "paymentType", "type": "dropdown", "required": true, "title": "Payment Type", "values": [{"name": "UPCC", "title": "银联 UnionPay"}, {"name": "WCPY", "title": "微信支付 WeChat Pay"}, {"name": "APWC", "title": "支付宝 Alipay"}, {"name": "UPDC", "title": "银联 UnionPay"}]}, "creditCardNumber": {"id": "creditCardNumber", "type": "text", "required": true, "title": "Card Number", "placeholder": "Required"}, "creditCardExpirationMonth": {"id": "creditCardExpirationMonth", "type": "dropdown", "required": true, "title": "Month", "placeholder": "MM", "values": [{"name": "1", "title": "January"}, {"name": "2", "title": "February"}, {"name": "3", "title": "March"}, {"name": "4", "title": "April"}, {"name": "5", "title": "May"}, {"name": "6", "title": "June"}, {"name": "7", "title": "July"}, {"name": "8", "title": "August"}, {"name": "9", "title": "September"}, {"name": "10", "title": "October"}, {"name": "11", "title": "November"}, {"name": "12", "title": "December"}]}, "creditCardExpirationYear": {"id": "creditCardExpirationYear", "type": "dropdown", "required": true, "title": "Year", "placeholder": "YYYY", "values": [{"name": "2024", "title": "2024"}, {"name": "2025", "title": "2025"}, {"name": "2026", "title": "2026"}, {"name": "2027", "title": "2027"}, {"name": "2028", "title": "2028"}, {"name": "2029", "title": "2029"}, {"name": "2030", "title": "2030"}, {"name": "2031", "title": "2031"}, {"name": "2032", "title": "2032"}, {"name": "2033", "title": "2033"}, {"name": "2034", "title": "2034"}, {"name": "2035", "title": "2035"}, {"name": "2036", "title": "2036"}, {"name": "2037", "title": "2037"}, {"name": "2038", "title": "2038"}, {"name": "2039", "title": "2039"}, {"name": "2040", "title": "2040"}, {"name": "2041", "title": "2041"}, {"name": "2042", "title": "2042"}, {"name": "2043", "title": "2043"}]}, "creditVerificationNumber": {"id": "creditVerificationNumber", "type": "text", "required": true, "title": "Security Code", "placeholder": "Security Code"}, "company": {"id": "company", "type": "text", "required": false}}, "sections": {"billingAddress": {"id": "billing<PERSON><PERSON>ress", "lines": [["paymentType"], ["creditCardNumber", "creditCardExpirationMonth", "creditCardExpirationYear", "creditVerificationNumber"], ["lastName", "firstName"], ["line1"], ["line2"], ["line3"], ["city"], ["postalCode", "stateProvince", "country"], ["phoneNumber", "phoneCountryDialCode"]]}, "name": {"id": "name", "lines": [["lastName", "firstName"]]}, "address": {"id": "address", "lines": [["line1"], ["line2"], ["line3"], ["city"], ["postalCode", "stateProvince", "country"]]}, "phone": {"id": "phone", "lines": [["phoneNumber", "phoneCountryDialCode"]]}, "payment": {"id": "payment", "lines": [["paymentType"], ["creditCardNumber", "creditCardExpirationMonth", "creditCardExpirationYear", "creditVerificationNumber"]]}, "shippingAddress": {"id": "shippingAddress", "lines": [["lastName", "firstName"], ["company"], ["line1"], ["line2"], ["line3"], ["city"], ["postalCode", "stateProvince", "country"]]}, "primaryAddress": {"id": "primaryAddress", "lines": [["lastName", "firstName"], ["line1"], ["line2"], ["line3"], ["city"], ["postalCode", "stateProvince", "country"]]}}}, "countryDialCodes": [{"dialCode": "93", "name": "Afghanistan", "displayName": "Afghanistan"}, {"dialCode": "355", "name": "Albania", "displayName": "Albania"}, {"dialCode": "213", "name": "Algeria", "displayName": "Algeria"}, {"dialCode": "244", "name": "Angola", "displayName": "Angola"}, {"dialCode": "1264", "name": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>"}, {"dialCode": "1268", "name": "Antigua and Barbuda", "displayName": "Antigua and Barbuda"}, {"dialCode": "54", "name": "Argentina", "displayName": "Argentina"}, {"dialCode": "374", "name": "Armenia", "displayName": "Armenia"}, {"dialCode": "61", "name": "Australia", "displayName": "Australia"}, {"dialCode": "43", "name": "Austria", "displayName": "Austria"}, {"dialCode": "994", "name": "Azerbaijan", "displayName": "Azerbaijan"}, {"dialCode": "1242", "name": "Bahamas", "displayName": "Bahamas"}, {"dialCode": "973", "name": "Bahrain", "displayName": "Bahrain"}, {"dialCode": "1246", "name": "Barbados", "displayName": "Barbados"}, {"dialCode": "375", "name": "Belarus", "displayName": "Belarus"}, {"dialCode": "32", "name": "Belgium", "displayName": "Belgium"}, {"dialCode": "501", "name": "Belize", "displayName": "Belize"}, {"dialCode": "229", "name": "Benin", "displayName": "Benin"}, {"dialCode": "1441", "name": "Bermuda", "displayName": "Bermuda"}, {"dialCode": "975", "name": "Bhutan", "displayName": "Bhutan"}, {"dialCode": "591", "name": "Bolivia", "displayName": "Bolivia"}, {"dialCode": "387", "name": "Bosnia and Herzegovina", "displayName": "Bosnia and Herzegovina"}, {"dialCode": "267", "name": "Botswana", "displayName": "Botswana"}, {"dialCode": "55", "name": "Brazil", "displayName": "Brazil"}, {"dialCode": "1284", "name": "British Virgin Islands", "displayName": "British Virgin Islands"}, {"dialCode": "673", "name": "Brunei", "displayName": "Brunei"}, {"dialCode": "359", "name": "Bulgaria", "displayName": "Bulgaria"}, {"dialCode": "226", "name": "Burkina Faso", "displayName": "Burkina Faso"}, {"dialCode": "855", "name": "Cambodia", "displayName": "Cambodia"}, {"dialCode": "237", "name": "Cameroon", "displayName": "Cameroon"}, {"dialCode": "1", "name": "Canada", "displayName": "Canada"}, {"dialCode": "238", "name": "Cape Verde", "displayName": "Cape Verde"}, {"dialCode": "1345", "name": "Cayman Islands", "displayName": "Cayman Islands"}, {"dialCode": "235", "name": "Chad", "displayName": "Chad"}, {"dialCode": "56", "name": "Chile", "displayName": "Chile"}, {"dialCode": "86", "name": "China", "displayName": "China mainland"}, {"dialCode": "57", "name": "Colombia", "displayName": "Colombia"}, {"dialCode": "243", "name": "Congo, Democratic Republic of", "displayName": "Congo, Democratic Republic of the"}, {"dialCode": "242", "name": "Congo, Republic of", "displayName": "Congo, Republic of the"}, {"dialCode": "506", "name": "Costa Rica", "displayName": "Costa Rica"}, {"dialCode": "385", "name": "Croatia", "displayName": "Croatia"}, {"dialCode": "357", "name": "Cyprus", "displayName": "Cyprus"}, {"dialCode": "420", "name": "Czechia", "displayName": "Czechia"}, {"dialCode": "225", "name": "Cote D'Ivoire", "displayName": "Côte d'Ivoire"}, {"dialCode": "45", "name": "Denmark", "displayName": "Denmark"}, {"dialCode": "1767", "name": "Dominica", "displayName": "Dominica"}, {"dialCode": "1", "name": "Dominican Republic", "displayName": "Dominican Republic"}, {"dialCode": "593", "name": "Ecuador", "displayName": "Ecuador"}, {"dialCode": "20", "name": "Egypt", "displayName": "Egypt"}, {"dialCode": "503", "name": "El Salvador", "displayName": "El Salvador"}, {"dialCode": "372", "name": "Estonia", "displayName": "Estonia"}, {"dialCode": "268", "name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"dialCode": "679", "name": "Fiji", "displayName": "Fiji"}, {"dialCode": "358", "name": "Finland", "displayName": "Finland"}, {"dialCode": "33", "name": "France", "displayName": "France"}, {"dialCode": "241", "name": "Gabon", "displayName": "Gabon"}, {"dialCode": "220", "name": "Gambia", "displayName": "Gambia"}, {"dialCode": "995", "name": "Georgia", "displayName": "Georgia"}, {"dialCode": "49", "name": "Germany", "displayName": "Germany"}, {"dialCode": "233", "name": "Ghana", "displayName": "Ghana"}, {"dialCode": "30", "name": "Greece", "displayName": "Greece"}, {"dialCode": "1473", "name": "Grenada", "displayName": "Grenada"}, {"dialCode": "502", "name": "Guatemala", "displayName": "Guatemala"}, {"dialCode": "245", "name": "Guinea-Bissau", "displayName": "Guinea-Bissau"}, {"dialCode": "592", "name": "Guyana", "displayName": "Guyana"}, {"dialCode": "504", "name": "Honduras", "displayName": "Honduras"}, {"dialCode": "852", "name": "Hong Kong", "displayName": "Hong Kong"}, {"dialCode": "36", "name": "Hungary", "displayName": "Hungary"}, {"dialCode": "354", "name": "Iceland", "displayName": "Iceland"}, {"dialCode": "91", "name": "India", "displayName": "India"}, {"dialCode": "62", "name": "Indonesia", "displayName": "Indonesia"}, {"dialCode": "964", "name": "Iraq", "displayName": "Iraq"}, {"dialCode": "353", "name": "Ireland", "displayName": "Ireland"}, {"dialCode": "972", "name": "Israel", "displayName": "Israel"}, {"dialCode": "39", "name": "Italy", "displayName": "Italy"}, {"dialCode": "1876", "name": "Jamaica", "displayName": "Jamaica"}, {"dialCode": "81", "name": "Japan", "displayName": "Japan"}, {"dialCode": "962", "name": "Jordan", "displayName": "Jordan"}, {"dialCode": "7", "name": "Kazakhstan", "displayName": "Kazakhstan"}, {"dialCode": "254", "name": "Kenya", "displayName": "Kenya"}, {"dialCode": "82", "name": "South Korea", "displayName": "Korea, Republic of"}, {"dialCode": "383", "name": "Kosovo", "displayName": "Kosovo"}, {"dialCode": "965", "name": "Kuwait", "displayName": "Kuwait"}, {"dialCode": "996", "name": "Kyrgyzstan", "displayName": "Kyrgyzstan"}, {"dialCode": "856", "name": "Laos", "displayName": "Laos"}, {"dialCode": "371", "name": "Latvia", "displayName": "Latvia"}, {"dialCode": "961", "name": "Lebanon", "displayName": "Lebanon"}, {"dialCode": "231", "name": "Liberia", "displayName": "Liberia"}, {"dialCode": "218", "name": "Libya", "displayName": "Libya"}, {"dialCode": "370", "name": "Lithuania", "displayName": "Lithuania"}, {"dialCode": "352", "name": "Luxembourg", "displayName": "Luxembourg"}, {"dialCode": "853", "name": "Macau", "displayName": "Macao"}, {"dialCode": "261", "name": "Madagascar", "displayName": "Madagascar"}, {"dialCode": "265", "name": "Malawi", "displayName": "Malawi"}, {"dialCode": "60", "name": "Malaysia", "displayName": "Malaysia"}, {"dialCode": "960", "name": "Maldives", "displayName": "Maldives"}, {"dialCode": "223", "name": "Mali", "displayName": "Mali"}, {"dialCode": "356", "name": "Malta", "displayName": "Malta"}, {"dialCode": "222", "name": "Mauritania", "displayName": "Mauritania"}, {"dialCode": "230", "name": "Mauritius", "displayName": "Mauritius"}, {"dialCode": "52", "name": "Mexico", "displayName": "Mexico"}, {"dialCode": "691", "name": "Micronesia", "displayName": "Micronesia"}, {"dialCode": "373", "name": "Moldova", "displayName": "Moldova"}, {"dialCode": "976", "name": "Mongolia", "displayName": "Mongolia"}, {"dialCode": "382", "name": "Montenegro", "displayName": "Montenegro"}, {"dialCode": "1664", "name": "Montserrat", "displayName": "Montserrat"}, {"dialCode": "212", "name": "Morocco", "displayName": "Morocco"}, {"dialCode": "258", "name": "Mozambique", "displayName": "Mozambique"}, {"dialCode": "95", "name": "Myanmar", "displayName": "Myanmar"}, {"dialCode": "264", "name": "Namibia", "displayName": "Namibia"}, {"dialCode": "674", "name": "Nauru", "displayName": "Nauru"}, {"dialCode": "977", "name": "Nepal", "displayName": "Nepal"}, {"dialCode": "31", "name": "Netherlands", "displayName": "Netherlands"}, {"dialCode": "64", "name": "New Zealand", "displayName": "New Zealand"}, {"dialCode": "505", "name": "Nicaragua", "displayName": "Nicaragua"}, {"dialCode": "227", "name": "Niger", "displayName": "Niger"}, {"dialCode": "234", "name": "Nigeria", "displayName": "Nigeria"}, {"dialCode": "389", "name": "North Macedonia", "displayName": "North Macedonia"}, {"dialCode": "47", "name": "Norway", "displayName": "Norway"}, {"dialCode": "968", "name": "Oman", "displayName": "Oman"}, {"dialCode": "92", "name": "Pakistan", "displayName": "Pakistan"}, {"dialCode": "680", "name": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>"}, {"dialCode": "507", "name": "Panama", "displayName": "Panama"}, {"dialCode": "675", "name": "Papua New Guinea", "displayName": "Papua New Guinea"}, {"dialCode": "595", "name": "Paraguay", "displayName": "Paraguay"}, {"dialCode": "51", "name": "Peru", "displayName": "Peru"}, {"dialCode": "63", "name": "Philippines", "displayName": "Philippines"}, {"dialCode": "48", "name": "Poland", "displayName": "Poland"}, {"dialCode": "351", "name": "Portugal", "displayName": "Portugal"}, {"dialCode": "974", "name": "Qatar", "displayName": "Qatar"}, {"dialCode": "40", "name": "Romania", "displayName": "Romania"}, {"dialCode": "7", "name": "Russia", "displayName": "Russia"}, {"dialCode": "250", "name": "Rwanda", "displayName": "Rwanda"}, {"dialCode": "966", "name": "Saudi Arabia", "displayName": "Saudi Arabia"}, {"dialCode": "221", "name": "Senegal", "displayName": "Senegal"}, {"dialCode": "381", "name": "Serbia", "displayName": "Serbia"}, {"dialCode": "248", "name": "Seychelles", "displayName": "Seychelles"}, {"dialCode": "232", "name": "Sierra Leone", "displayName": "Sierra Leone"}, {"dialCode": "65", "name": "Singapore", "displayName": "Singapore"}, {"dialCode": "421", "name": "Slovakia", "displayName": "Slovakia"}, {"dialCode": "386", "name": "Slovenia", "displayName": "Slovenia"}, {"dialCode": "677", "name": "Solomon Islands", "displayName": "Solomon Islands"}, {"dialCode": "27", "name": "South Africa", "displayName": "South Africa"}, {"dialCode": "34", "name": "Spain", "displayName": "Spain"}, {"dialCode": "94", "name": "Sri Lanka", "displayName": "Sri Lanka"}, {"dialCode": "1869", "name": "Saint Kitts and Nevis", "displayName": "St. Kitts and Nevis"}, {"dialCode": "1758", "name": "Saint Lucia", "displayName": "St. Lucia"}, {"dialCode": "1784", "name": "Saint Vincent and the Grenadines", "displayName": "St. Vincent and the Grenadines"}, {"dialCode": "597", "name": "Suriname", "displayName": "Suriname"}, {"dialCode": "46", "name": "Sweden", "displayName": "Sweden"}, {"dialCode": "41", "name": "Switzerland", "displayName": "Switzerland"}, {"dialCode": "239", "name": "São Tomé and Príncipe", "displayName": "São Tomé and Príncipe"}, {"dialCode": "886", "name": "Taiwan", "displayName": "Taiwan"}, {"dialCode": "992", "name": "Tajikistan", "displayName": "Tajikistan"}, {"dialCode": "255", "name": "Tanzania", "displayName": "Tanzania"}, {"dialCode": "66", "name": "Thailand", "displayName": "Thailand"}, {"dialCode": "676", "name": "Tonga", "displayName": "Tonga"}, {"dialCode": "1868", "name": "Trinidad and Tobago", "displayName": "Trinidad and Tobago"}, {"dialCode": "216", "name": "Tunisia", "displayName": "Tunisia"}, {"dialCode": "993", "name": "Turkmenistan", "displayName": "Turkmenistan"}, {"dialCode": "1649", "name": "Turks and Caicos", "displayName": "Turks and Caicos Islands"}, {"dialCode": "90", "name": "Türkiye", "displayName": "Türkiye"}, {"dialCode": "256", "name": "Uganda", "displayName": "Uganda"}, {"dialCode": "380", "name": "Ukraine", "displayName": "Ukraine"}, {"dialCode": "971", "name": "United Arab Emirates", "displayName": "United Arab Emirates"}, {"dialCode": "44", "name": "United Kingdom", "displayName": "United Kingdom"}, {"dialCode": "1", "name": "United States", "displayName": "United States"}, {"dialCode": "598", "name": "Uruguay", "displayName": "Uruguay"}, {"dialCode": "998", "name": "Uzbekistan", "displayName": "Uzbekistan"}, {"dialCode": "678", "name": "Vanuatu", "displayName": "Vanuatu"}, {"dialCode": "58", "name": "Venezuela", "displayName": "Venezuela"}, {"dialCode": "84", "name": "Vietnam", "displayName": "Vietnam"}, {"dialCode": "967", "name": "Yemen", "displayName": "Yemen"}, {"dialCode": "260", "name": "Zambia", "displayName": "Zambia"}, {"dialCode": "263", "name": "Zimbabwe", "displayName": "Zimbabwe"}], "paymentMethodUpdateAllowed": true, "familyPaymentUpdateAllowed": true, "paymentRemovalAllowed": false, "iTunesThickAppAccountSummaryUrl": "itms://buy.itunes.apple.com/WebObjects/MZFinance.woa/wa/accountSummary", "supportsPlatformAssets": false, "paymentStatus": "LOADED", "primaryPaymentFamilyPayment": false, "paymentSupportedInWebButNotCurrentClient": false, "enableAlipayV2OnPurple": false, "displayUnsupportedPaymentMessage": false, "primaryCardImagePath": "/images/wcpy.png", "primaryCardImagePath2x": "/images/<EMAIL>", "primaryCardImagePath3x": "/images/<EMAIL>", "surfImagePath": "/images/platform/applecash.png", "surfImagePath2x": "/images/platform/<EMAIL>", "surfImagePath3x": "/images/platform/<EMAIL>", "applePayBadgeImagePath": "/images/applepay.png", "applePayBadgeImagePath2x": "/images/<EMAIL>", "broadwayImagePath": "/images/platform/applecard.png", "broadwayImagePath2x": "/images/platform/<EMAIL>", "broadwayImagePath3x": "/images/platform/<EMAIL>"}