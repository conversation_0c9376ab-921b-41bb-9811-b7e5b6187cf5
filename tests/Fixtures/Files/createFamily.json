{"status-message": "Member of a family.", "familyInvitations": [], "outgoingTransferRequests": [], "isMemberOfFamily": true, "family": {"familyId": "3038411018803674", "transferRequests": [], "invitations": [], "organizer": "20186096743", "members": ["20186096743"], "outgoingTransferRequests": [], "etag": "2"}, "familyMembers": [{"lastName": "jack", "dsid": "20186096743", "originalInvitationEmail": "<EMAIL>", "fullName": "chang jack", "ageClassification": "ADULT", "appleIdForPurchases": "<EMAIL>", "appleId": "<EMAIL>", "familyId": "3038411018803674", "firstName": "chang", "hasParentalPrivileges": true, "hasScreenTimeEnabled": false, "hasAskToBuyEnabled": false, "hasSharePurchasesEnabled": true, "shareMyLocationEnabledFamilyMembers": [], "hasShareMyLocationEnabled": true, "dsidForPurchases": "20186096743"}], "status": 0, "showAddMemberButton": true}