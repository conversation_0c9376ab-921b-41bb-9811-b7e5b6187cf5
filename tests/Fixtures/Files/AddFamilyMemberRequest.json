{"status-message": "Member of a family.", "familyInvitations": [], "outgoingTransferRequests": [], "isMemberOfFamily": true, "family": {"familyId": "3038411018722656", "transferRequests": [], "invitations": [], "organizer": "20186096743", "members": ["20186096743", "21905965912"], "outgoingTransferRequests": [], "etag": "3"}, "familyMembers": [{"lastName": "jack", "dsid": "20186096743", "originalInvitationEmail": "<EMAIL>", "fullName": "chang jack", "ageClassification": "ADULT", "appleIdForPurchases": "<EMAIL>", "appleId": "<EMAIL>", "familyId": "3038411018722656", "firstName": "chang", "hasParentalPrivileges": true, "hasScreenTimeEnabled": false, "hasAskToBuyEnabled": false, "hasSharePurchasesEnabled": true, "shareMyLocationEnabledFamilyMembers": [], "hasShareMyLocationEnabled": true, "dsidForPurchases": "20186096743"}, {"lastName": "jack", "dsid": "21905965912", "originalInvitationEmail": "<EMAIL>", "fullName": "chang jack", "ageClassification": "ADULT", "appleIdForPurchases": "<EMAIL>", "appleId": "<EMAIL>", "familyId": "3038411018722656", "firstName": "chang", "hasParentalPrivileges": false, "hasScreenTimeEnabled": false, "hasAskToBuyEnabled": false, "hasSharePurchasesEnabled": true, "hasShareMyLocationEnabled": true, "dsidForPurchases": "21905965912"}], "status": 0, "showAddMemberButton": true}