{"statusCode": 200, "headers": {"Server": "Apple", "Date": "<PERSON><PERSON>, 17 Dec 2024 07:53:26 GMT", "Content-Type": "application/json;charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "X-Apple-I-Request-ID": "ff19e1f5-bc4b-11ef-a3c7-c3dd929cd606", "X-FRAME-OPTIONS": "DENY", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload", "Content-Security-Policy": "default-src 'self' ; child-src blob: ; connect-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://webcourier.sandbox.push.apple.com https://xp-qa.apple.com ; font-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; frame-src 'self' https://appleid.apple.com https://gsa.apple.com https://account.apple.com ; img-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://*.mzstatic.com data: https://*.apple.com ; media-src data: ; object-src 'none' ; script-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; style-src 'unsafe-inline' 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ;", "Referrer-Policy": "origin", "X-BuildVersion": "R3", "scnt": "AAAA-kFBNzc1QjM2RTZDQjY4NEFEQkFDMURENDVERjJCMTAyMEZDNjc5ODk4MDgyRDMzNjY3RkFENjVENTcwQUExMTIzRTNEMzA1MDU1ODQ5QjczMzVCQTBCRTNBMDYzMTEzNEQzMEM3RjVBMkQ5NTEzMkY3MTA1QzQ0OTIyNTNCRDczNzk4M0VFQjhGMUNBNzcwRkNDMDYwOEE3M0ZFREQyRDM5NTMxMjEyMThEMERDRjE1REExN0MyRDUzNEJEQTA0RUY4N0RGM0NFMjJERUU2MzQ5MzM4RkU1NEQyN0Q3QkQwMDlDMTBCMzE2NDE3NzU0RXw1AAABk9OpRLBjk7MrGwMPbZFdtO-kHOx2-gLxHzLMxwnv5SxDN-BF3RfUGeKHrErfABbc99iVIHwr5_AycHE9-GDJUiRoRQxZy21GQYTvT-7-0UfEBVRUOg", "Set-Cookie": ["dslang=CN-ZH; Domain=apple.com; Path=/; Secure; HttpOnly", "site=CHN; Domain=apple.com; Path=/; Secure; HttpOnly", "crsc=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:10 GMT; Domain=idmsa.apple.com; Path=/; Secure; HttpOnly"], "Pragma": "no-cache", "Expires": "Thu, 01 Jan 1970 00:00:00 GMT", "Cache-Control": ["no-cache", "no-store", "no-store"], "X-Apple-ID-Session-Id": "AA775B36E6CB684ADBAC1DD45DF2B1020FC679898082D33667FAD65D570AA1123E3D305055849B7335BA0BE3A0631134D30C7F5A2D95132F7105C4492253BD737983EEB8F1CA770FCC0608A73FEDD2D3953121218D0DCF15DA17C2D534BDA04EF87DF3CE22DEE6349338FE54D27D7BD009C10B316417754E", "X-Apple-Auth-Attributes": "IzzdGLfmMKghjPj+hdW7csBDEHn0MtSJzh0x5R4s6KFaLX7J5DVccN0Vf3i7tThhWWTmqADOG8YIQxJgP3PB7K8QRQLyxkbkh7+gAjyfF7ml8nDG50QTzWk8AZn1TcAQlYVH6CJZa0fo7qeFkb2M/CoQ4w9JRRtG08UsgyDjr/v/Sz/xUCKsTI5HtVVacSpFr2JkZHpxQg2XABbc9/toTLE=", "X-Apple-Session-Token": "wsQvSaw04qk2Fjn8Q2xhse6RzmsnzvFL0f5LPr9WE4nWQ86QaaWaAgDHM3Ny9jeVE71Jp1Mp9fBl+YWRyVoR5mv+/Ur0vFdtYUP0pUKyIF6+7ekfdwxDEn2wkOtjQ4M7ViUr1ATqEsdxo5/4fUd+ftRwgjJ2uQH0KMvgWwLdghFxOA7MXrSarFlNHAGwrl+Tqb3oZHSkMBAKDfbcJeaV0Ay5OFyMDqborzWWE+SlJPmMSIe7XfV58hYcSTNnhEJoPdRt6qW1rcjeYwtvGSBnUrZz+Jk1JqZfP95OPpuGPElSuE8uEdy3Vdk2jWQk4b7W2DgwdMrHjGGrLHX36HLQkH+uqQGLV1dPsQYU7EAuvGGCSmr/V19aWIlI9bk1pPFbajEQNMjy97q6DQEbXsv1kaBMAuUbHkzRiFl3Ad41xgff1jQ3gA4ighmKgWH1bwesYwMFTf8s21/BHPQaUIYU7kjbWb6kQ/XFhtILHPpmjKx8WTfeu6ISySXlABBB/HSZysv40PSr+YNFsVt/StLV6bC+q1wGjFCWbi51q/BMRpvSosz1eewG4gMinyqr0nsC+QCMxCcqSrroR7YOuKjEmJ7nrJ/rSKvOVMwK7DLoEV87s4mKJ/Yu91IgneSXqW1vIm2t2s7PGoMTRr5brAd47lBIPuhScxZfXcSS+8h4tFAs6wqm+VAynTkjG9hf1VrfBTUGFWFata237v2Ge6CeEYA6clblRPUF47E/L6ogpyBool2bZaafDct7PRlKAhW9NJMlV9vjpEnHvQQe9yV5axPZ5mKP2ns1EhZCVb+eJ12RTTklS45r4wLvnrVKu1mQic7lzCXrZeNcs65LL9r1HFe6oiUIOLDNDZFqsmRhpLKnKTuIf5WKG+2HW+f5yPYSrR/37gPBqWGYJ6NWi24Iw7jrMeT5XOOO5cEHCmbTviGBCvNgYWcgtxybF4L53lDYRnt9QFIG/ZIC7+nBYhaVdSjxQaalC020iQPxcKq4p5vc+ODJ6uwEOCGKH45tAqTaYOsgl/A6xi/+sLqAr0OEFTDJdulEEem51v5F+Ni/Ldr4R+GPKs9AM9rrDnV/MMff3ooUcOlLlnNddyfOy+2ciN9HtUctjJbfktGheoBoUGv5ABbc9/x0b6c=", "X-Apple-ID-Account-Country": "CHN", "vary": "accept-encoding", "Content-Language": "zh-CN-x-lvariant-CHN"}, "data": "{\n  \"trustedPhoneNumbers\" : [ {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 2\n  }, {\n    \"numberWithDialCode\" : \"+86 ••• •••• ••70\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"••• •••• ••70\",\n    \"lastTwoDigits\" : \"70\",\n    \"id\" : 1\n  } ],\n  \"phoneNumber\" : {\n    \"numberWithDialCode\" : \"+852 9321 0810\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"••••••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 2\n  },\n  \"securityCode\" : {\n    \"code\" : \"913714\",\n    \"tooManyCodesSent\" : false,\n    \"tooManyCodesValidated\" : false,\n    \"securityCodeLocked\" : false,\n    \"securityCodeCooldown\" : false,\n    \"valid\" : true\n  },\n  \"mode\" : \"sms\",\n  \"type\" : \"verification\",\n  \"authenticationType\" : \"hsa2\",\n  \"recoveryUrl\" : \"https://iforgot.apple.com/phone/add?prs_account_nm=sjx5weaver%40hotmail.com&autoSubmitAccount=true&appId=142\",\n  \"cantUsePhoneNumberUrl\" : \"https://iforgot.apple.com/iforgot/phone/add?context=cantuse&prs_account_nm=sjx5weaver%40hotmail.com&autoSubmitAccount=true&appId=142\",\n  \"recoveryWebUrl\" : \"https://iforgot.apple.com/password/verify/appleid?prs_account_nm=sjx5weaver%40hotmail.com&autoSubmitAccount=true&appId=142\",\n  \"repairPhoneNumberUrl\" : \"https://gsa.apple.com/appleid/account/manage/repair/verify/phone\",\n  \"repairPhoneNumberWebUrl\" : \"https://appleid.apple.com/widget/account/repair?#!repair\",\n  \"aboutTwoFactorAuthenticationUrl\" : \"https://support.apple.com/kb/HT204921\",\n  \"autoVerified\" : false,\n  \"showAutoVerificationUI\" : false,\n  \"supportsCustodianRecovery\" : false,\n  \"hideSendSMSCodeOption\" : false,\n  \"supervisedChangePasswordFlow\" : false,\n  \"enableNonFTEU\" : false,\n  \"trustedPhoneNumber\" : {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 2\n  },\n  \"hsa2Account\" : true,\n  \"restrictedAccount\" : false,\n  \"supportsRecovery\" : true,\n  \"managedAccount\" : false\n}"}