{"statusCode": 200, "headers": {"Server": "Apple", "Date": "Tu<PERSON>, 17 Dec 2024 09:53:11 GMT", "Content-Type": "application/json;charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "X-Apple-I-Request-ID": "b95f7aea-bc5c-11ef-8630-9d94a9426ef1", "X-FRAME-OPTIONS": "DENY", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload", "Content-Security-Policy": "default-src 'self' ; child-src blob: ; connect-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://webcourier.sandbox.push.apple.com https://xp-qa.apple.com ; font-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; frame-src 'self' https://appleid.apple.com https://gsa.apple.com https://account.apple.com ; img-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://*.mzstatic.com data: https://*.apple.com ; media-src data: ; object-src 'none' ; script-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; style-src 'unsafe-inline' 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ;", "Referrer-Policy": "origin", "X-BuildVersion": "R3", "scnt": "AAAA-kJEMkRDRDUxRjZBMkVCMTM0NTJCMzI1MEYxQjQwMzk1RENFQkU4N0RDMzk3M0FFMUZGMTk5NDY2NzY4RjAzMUNBMDhBMjBDMjRDNTI2QzQ5MDNGNzAxMEFCODQ5RTJDNzJFQTJEMzc1OEJEQzdBQUE3QjY1REM3RUEwQUJEQUEzOTJDOUYwQjNGQkZDMURFRTZGODNCNURERjA4Q0FFMUE1NUFBNURBRjdBNTU5NDEwM0FBNjMwRjY5MkQ2OTkwQkIwODdBQTVFNDNENTQ3RDczMUNENDE1Q0E5MkJBMjEzNjg2M0JFMjU3Rjg0OTA0NHw1AAABk9QW5P93Fixnp1z7bu2hs-qeRbCj8Yen5J9fm_wKoE2TO6Y1HHlDTuyNJ1nMABLmCd-foVN52aEMdH4AOC3jgkamQPoPo44Btbcu4znPvf4N5k1ebQ", "Set-Cookie": ["dslang=CN-ZH; Domain=apple.com; Path=/; Secure; HttpOnly", "site=CHN; Domain=apple.com; Path=/; Secure; HttpOnly", "crsc=; Max-Age=0; Expires=Thu, 01 Jan 1970 00:00:10 GMT; Domain=idmsa.apple.com; Path=/; Secure; HttpOnly"], "Pragma": "no-cache", "Expires": "Thu, 01 Jan 1970 00:00:00 GMT", "Cache-Control": ["no-cache", "no-store", "no-store"], "X-Apple-ID-Session-Id": "BD2DCD51F6A2EB13452B3250F1B40395DCEBE87DC3973AE1FF199466768F031CA08A20C24C526C4903F7010AB849E2C72EA2D3758BDC7AAA7B65DC7EA0ABDAA392C9F0B3FBFC1DEE6F83B5DDF08CAE1A55AA5DAF7A5594103AA630F692D6990BB087AA5E43D547D731CD415CA92BA2136863BE257F849044", "X-Apple-Auth-Attributes": "ixocnOqAx/PAQtvkOQz6jrWJhgbBUqnlyXRlzSXeSRlBJj9einm0m8hji3oGZnGEvIxfbDi0Eyn50SYOPpb7MkcUiy4Fv9XQe75mEt1P4t7COmLv6CrJwvhOc9tQQIJtchPsXcyCf51jODTBfnPS8YjHNZiV4WvqNhhjrm48L2FjV+Tiw0H5T7cTWRtpS7zvfUMDhv9qajUEABLmCgJP4KQ=", "X-Apple-Session-Token": "gyECSPKYk4ytHbjoyKPqrqUgy51D+4R0b1vAl7V42dB9I+tks41c1PCC+NW/PnEw6vHkbTDgyQ62G/XZsFrMJpwApODdzcoRk2GzP/xW+kLqkv6iSc2ctf0PoT2UoEvMZ0MT8ReQ3GdVkhcaKCirHsiSjd/6nXB+zl1Dg3/up2FWNZmBRGDaCgJIPRJhS98P1SbIRC7I8zUGd9kvppoX6CoUq68bO3bbl9VEUq9vwL0K8WWigtZBWfpJ2fFKzTA1Yuvhbu7XdWXHUxTPYHHxt6wP3AmIVzetHU6+BQN8EEwF5nmJRt5SIMOjyvsA0PiVyodbWqW68EulHNqE4C39jL7tRpQOZvKdr+xnP0PVauGz672m2Yho9onwxEQjbb/3XSwSv7k4nP715Zl2FFW2BiElHqO0q8wA65bxoCyS6WJECH8S2Qpt3rCv85z0fIu2wN8L+BggIPLDbL+UAklAgGm6030DdeqI/pkQ7OlEj2trBHKbZbSehYLDYTN+dp+7pvJ90fJEe7HbW6ZMTp0YYJEuUkro0+5xK4SndzzxA/XHPpxXTLbPU6aP/OABuCDa2o5+TH+GIRQ8DL/gXuPcDh0/PZTrl5HzE7ZHa4uQTEdgIBOy2tGsZi9mcbSMp5WokVMNsXYuCUpi7YoBHQitpelAaHjDyhgpKDqrrR2gnYDiFL1oI+A18En2THwZvIYuS5LHSuKGWRWJATNm7GhV3YnDV15z0ovMR/6ePTWcO15r0x6BTDRjbt3TRI505e164tZ5EJm7qVm/cchANEVGEDIv6dhqMkT18N5FbUaDXTOa7FOrIaaOcMvDLpluz21ERQ5Q97PFChCtBRjceHxiyFaeTGKJs4g+WK9feZlv3YGnOhVy/xEERGGM5XTBdpf4TPViZZl3kl6Xt+QAlhkcgjoQkz3VHfyDnSb55t8Qcs2J3CXNaBT6osSsrku0xKTxBQNBMmvbZ1h6py5TBYVm9+xcQvERBLjjW5PgLXuFHKwQ5igdIJ+3MpnFfBzODDw0egGRWJ6zI3MCnktf2v5LNQw55WjaL3YI1uzfmYBRHlE6Y7HYAn6F1oVvkimeJZQNfNhmCn4uNcw4x+WzuwmKCmD+Jf4RCkDC2ACwpBw8+dnBcGIAEuYKA7CFpg==", "X-Apple-ID-Account-Country": "CHN", "vary": "accept-encoding", "Content-Language": "zh-CN-x-lvariant-CHN"}, "data": "{\n  \"trustedPhoneNumbers\" : [ {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  }, {\n    \"numberWithDialCode\" : \"+86 ••• •••• ••21\",\n    \"nonFTEU\" : false,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"••• •••• ••21\",\n    \"lastTwoDigits\" : \"21\",\n    \"id\" : 2\n  } ],\n  \"phoneNumber\" : {\n    \"numberWithDialCode\" : \"+852 9321 0810\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"••••••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  },\n  \"securityCode\" : {\n    \"code\" : \"102047\",\n    \"tooManyCodesSent\" : false,\n    \"tooManyCodesValidated\" : false,\n    \"securityCodeLocked\" : false,\n    \"securityCodeCooldown\" : false,\n    \"valid\" : true\n  },\n  \"mode\" : \"sms\",\n  \"type\" : \"verification\",\n  \"authenticationType\" : \"hsa2\",\n  \"recoveryUrl\" : \"https://iforgot.apple.com/phone/add?prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"cantUsePhoneNumberUrl\" : \"https://iforgot.apple.com/iforgot/phone/add?context=cantuse&prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"recoveryWebUrl\" : \"https://iforgot.apple.com/password/verify/appleid?prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"repairPhoneNumberUrl\" : \"https://gsa.apple.com/appleid/account/manage/repair/verify/phone\",\n  \"repairPhoneNumberWebUrl\" : \"https://appleid.apple.com/widget/account/repair?#!repair\",\n  \"aboutTwoFactorAuthenticationUrl\" : \"https://support.apple.com/kb/HT204921\",\n  \"autoVerified\" : false,\n  \"showAutoVerificationUI\" : false,\n  \"supportsCustodianRecovery\" : false,\n  \"hideSendSMSCodeOption\" : false,\n  \"supervisedChangePasswordFlow\" : false,\n  \"enableNonFTEU\" : false,\n  \"trustedPhoneNumber\" : {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  },\n  \"hsa2Account\" : true,\n  \"restrictedAccount\" : false,\n  \"supportsRecovery\" : true,\n  \"managedAccount\" : false\n}"}