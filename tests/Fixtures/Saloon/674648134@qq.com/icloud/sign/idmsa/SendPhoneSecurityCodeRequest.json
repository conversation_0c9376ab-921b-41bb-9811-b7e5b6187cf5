{"statusCode": 200, "headers": {"Server": "Apple", "Date": "<PERSON><PERSON>, 17 Dec 2024 09:52:58 GMT", "Content-Type": "application/json;charset=UTF-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "X-Apple-I-Request-ID": "b1d6c4b9-bc5c-11ef-bd14-ef9558c79e18", "X-FRAME-OPTIONS": "DENY", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=********; includeSubDomains; preload", "Content-Security-Policy": "default-src 'self' ; child-src blob: ; connect-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://webcourier.sandbox.push.apple.com https://xp-qa.apple.com ; font-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; frame-src 'self' https://appleid.apple.com https://gsa.apple.com https://account.apple.com ; img-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://*.mzstatic.com data: https://*.apple.com ; media-src data: ; object-src 'none' ; script-src 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ; style-src 'unsafe-inline' 'self' https://www.apple.com https://appleid.cdn-apple.com https://idmsa.apple.com https://gsa.apple.com https://idmsa.apple.com.cn https://signin.apple.com ;", "Referrer-Policy": "origin", "X-BuildVersion": "R3", "scnt": "AAAA-kJEMkRDRDUxRjZBMkVCMTM0NTJCMzI1MEYxQjQwMzk1RENFQkU4N0RDMzk3M0FFMUZGMTk5NDY2NzY4RjAzMUNBMDhBMjBDMjRDNTI2QzQ5MDNGNzAxMEFCODQ5RTJDNzJFQTJEMzc1OEJEQzdBQUE3QjY1REM3RUEwQUJEQUEzOTJDOUYwQjNGQkZDMURFRTZGODNCNURERjA4Q0FFMUE1NUFBNURBRjdBNTU5NDEwM0FBNjMwRjY5MkQ2OTkwQkIwODdBQTVFNDNENTQ3RDczMUNENDE1Q0E5MkJBMjEzNjg2M0JFMjU3Rjg0OTA0NHw0AAABk9QWs509H14mDf-bNfoD7Wzn2rwFCfpkTkp8VTjAMCEnNCDJeBrTTOHwGig3ABOOW2KKk1WC1jJYrh1K5FFr6dVApVw4WLGBQn4JfNOnUsKXajwCRg", "Set-Cookie": ["dslang=CN-ZH; Domain=apple.com; Path=/; Secure; HttpOnly", "site=CHN; Domain=apple.com; Path=/; Secure; HttpOnly", "crsc=Ik7KCqQeLcInaJwyHSO9mXml1jHd0UoY5oCilqYrJUXoyegXAwttkHnhghIFXYw91L4wlBroLfBIb22+KwATjlt853Il; Max-Age=900000; Expires=Fri, 27 Dec 2024 19:52:58 GMT; Domain=idmsa.apple.com; Path=/; Secure; HttpOnly"], "Pragma": "no-cache", "Expires": "Thu, 01 Jan 1970 00:00:00 GMT", "Cache-Control": ["no-cache", "no-store", "no-store"], "X-Apple-ID-Session-Id": "BD2DCD51F6A2EB13452B3250F1B40395DCEBE87DC3973AE1FF199466768F031CA08A20C24C526C4903F7010AB849E2C72EA2D3758BDC7AAA7B65DC7EA0ABDAA392C9F0B3FBFC1DEE6F83B5DDF08CAE1A55AA5DAF7A5594103AA630F692D6990BB087AA5E43D547D731CD415CA92BA2136863BE257F849044", "X-Apple-Auth-Attributes": "413EywNypptM6JDtTkJFnng8W//2nWlZTwjFqmgUSj4ibgaJvapK74lngxxBp94T9G9ykOT1aRX3BcZF5vCQdFJCDPtqER0r0YAJthQOx8tAMiJCh6nMPJ3M4cFmMSXbNrvT7VmyolzZHReZbQ/sLbawuXBIt6spNR9cUdlPM/WxPPpeNuwbCt8mSwwKeeWCFzMKcZQRMMrBABOOW3zyZ+E=", "X-Apple-Session-Token": "D5590LOOEy0y2edNcwkP8TKbJSY4fT1JDTspG1kBzYpR/SLiKaLH04vsHzujCP7Lgq3o9UUoR5P0thEuP2MPcV1xg3VvanSZ0/o5aNAEWOiQVYjPID9cCMyGKrZEutqJGy814vEeqZHhTTy7e/V3E/OgpI1t82ox543mSMiBeO8IQXTeTUgDeU75u/ezmXGba0BFiMIm5aDAVFhL1F0INCOuJRa16UCqp6K3j5lDQgqHNgdUHIDtj8W7LH/mHvO0M2N4n6QF8Z5NPTBKUxeK6IvoNvtKXJNi58vRnR3OW8DDH96Vs/w46tBZNdPyfqW5ieeKOfAQ5iQj1ops+3GtYb46dim1HqOhSn0BrfmoBOScgHxpkqrb1sYb6PdTMzPcYnerxWkU7Apje8+OGahmmEzIhKhojrRlVLqd6BHWNPnuxn200j3K9IZZgA9bq4wBjnPncYzgBDhij1BqUXvDETc5WH9rQFExdqwDqCqcn5ZJA9dzS7+vqbo71x16DQQ6226CxkNxtLsdRPBqqK8jF2jafjTiGQ4ZemepyKKSTRTk9QHfRcuUhDrk1HAFk8WlXnSNkiQtqfiz0cxDAXFPuir9IubtrKtclESrqEucWrSlB51w/cMQ0ipyYo4jHUByTlvxMMMRQbBdO/oD+staZb3O4NWm+4l+X6q+tJxznLIhmKP0uxxRS0ZkRQ7gAvsX8Kve6bYMOPLdAmprD8SXQGHR06FYwet0NYdjg0FdC9EuQz0r4RxaSAi0AbrgPI+pHRzoqG0hDyeSdvixRvNgYjK3VmWKKdiKPWb8caGL62JBbiijvg5MRQSKlolCWIfRRT9x2T9Y2thE+9sZH4vm0IRi+1wy8Blit/srFwawBiIt5B0BQcycw1/ZqU81Yq+UlFdocBRoTmMX35fcnx9tJ2Z4Uk0BYbbL715E5g6JZyYn3rx0bFlo5aq9Tw4QMdWCX1024yFTeixvjtzih66ltw5Us2/KP1LmjEp9sjP8pg7hdxZRpL+hlFc+KEJqTi0wiP015PuYgReVPkVheha+rTrBB9mpABOOW3zztuw=", "X-Apple-ID-Account-Country": "CHN", "vary": "accept-encoding", "Content-Language": "zh-CN-x-lvariant-CHN"}, "data": "{\n  \"trustedPhoneNumbers\" : [ {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  }, {\n    \"numberWithDialCode\" : \"+86 ••• •••• ••21\",\n    \"nonFTEU\" : false,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"••• •••• ••21\",\n    \"lastTwoDigits\" : \"21\",\n    \"id\" : 2\n  } ],\n  \"phoneNumber\" : {\n    \"numberWithDialCode\" : \"•••• •••• ••10\",\n    \"nonFTEU\" : false,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  },\n  \"securityCode\" : {\n    \"length\" : 6,\n    \"tooManyCodesSent\" : false,\n    \"tooManyCodesValidated\" : false,\n    \"securityCodeLocked\" : false,\n    \"securityCodeCooldown\" : false\n  },\n  \"mode\" : \"sms\",\n  \"type\" : \"verification\",\n  \"authenticationType\" : \"hsa2\",\n  \"recoveryUrl\" : \"https://iforgot.apple.com/phone/add?prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"cantUsePhoneNumberUrl\" : \"https://iforgot.apple.com/iforgot/phone/add?context=cantuse&prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"recoveryWebUrl\" : \"https://iforgot.apple.com/password/verify/appleid?prs_account_nm=*********%40qq.com&autoSubmitAccount=true&appId=142\",\n  \"repairPhoneNumberUrl\" : \"https://gsa.apple.com/appleid/account/manage/repair/verify/phone\",\n  \"repairPhoneNumberWebUrl\" : \"https://appleid.apple.com/widget/account/repair?#!repair\",\n  \"aboutTwoFactorAuthenticationUrl\" : \"https://support.apple.com/kb/HT204921\",\n  \"autoVerified\" : false,\n  \"showAutoVerificationUI\" : false,\n  \"supportsCustodianRecovery\" : false,\n  \"hideSendSMSCodeOption\" : false,\n  \"supervisedChangePasswordFlow\" : false,\n  \"enableNonFTEU\" : false,\n  \"trustedPhoneNumber\" : {\n    \"numberWithDialCode\" : \"+852 •••• ••10\",\n    \"nonFTEU\" : true,\n    \"pushMode\" : \"sms\",\n    \"obfuscatedNumber\" : \"•••• ••10\",\n    \"lastTwoDigits\" : \"10\",\n    \"id\" : 3\n  },\n  \"hsa2Account\" : true,\n  \"restrictedAccount\" : false,\n  \"supportsRecovery\" : true,\n  \"managedAccount\" : false\n}"}