# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 11 application that serves as an Apple ID management system, handling Apple account verification, phone number binding, and security operations through automated processes. The system uses Filament for the admin panel and includes comprehensive queue processing, proxy management, and analytics tracking.

## Development Commands

### PHP/Laravel Commands
- `php artisan serve` - Start development server
- `php artisan migrate` - Run database migrations
- `php artisan migrate:fresh --seed` - Fresh migration with seeding
- `php artisan queue:work` - Process queue jobs
- `php artisan horizon` - Start Horizon queue dashboard
- `php artisan horizon:status` - Check Horizon status
- `php artisan make:filament-user` - Create admin user

### Frontend Commands
- `npm run dev` - Start Vite development server
- `npm run build` - Build frontend assets for production

### Testing Commands
- `php artisan test` - Run PHPUnit tests
- `./vendor/bin/pest` - Run Pest tests
- `./vendor/bin/phpunit` - Run specific PHPUnit tests

### Code Quality Commands
- `./vendor/bin/pint` - Laravel Pint code formatting
- `php artisan ide-helper:generate` - Generate IDE helper files

## Architecture Overview

### Core Components

**Controllers**
- `AppleClientController` - Primary controller handling Apple ID authentication workflows, security codes, and phone management

**Services**
- `AppleClientControllerService` - Core business logic for Apple ID operations
- `PhoneService` - Phone number formatting and validation using libphonenumber
- `AnalyticsService` - Web analytics and visitor tracking

**Models**
- `Account` - Primary entity for Apple ID accounts with relationships to logs, devices, payments, and family members
- `Phone` - Phone number management with status tracking (normal, invalid, bound, binding)
- `User` - Admin panel users with role-based access
- `AccountLogs` - Comprehensive audit trail for all account operations

**Queue Jobs**
- `BindAccountPhone` - Phone number binding with unique constraint and 10-minute timeout
- `SynchronousAppleIdSignInStatusJob` - Apple ID sign-in status synchronization
- `AppleidAddSecurityVerifyPhone` - Security verification phone number addition
- `ProcessAccountImport` - Bulk account import processing

### Middleware Stack
- `ApiRateLimiter` - Configurable rate limiting (30 requests/minute default)
- `CollectAnalyticsDataMiddleware` - Page visit tracking
- `BlackListIpsMiddleware` - IP blocking functionality
- `EnforceSecuritySettingsMiddleware` - Security policy enforcement

### Key Features
- Comprehensive proxy management for geographic distribution
- Cookie and header synchronization for Apple services
- Family sharing relationship management
- Web analytics with online user tracking
- Automated retry mechanisms with exponential backoff

## Configuration

### Environment Setup
- Copy `.env.example` to `.env`
- Configure database connection (MySQL recommended)
- Set up Redis for queues and caching
- Configure proxy settings if needed
- Set `APP_URL` to your domain

### Key Config Files
- `config/apple.php` - Apple service URLs and country codes
- `config/api.php` - Rate limiting configuration
- `config/horizon.php` - Queue processing settings
- `config/http-proxy-manager.php` - Proxy management settings

## Development Notes

### Testing Framework
- Uses Pest PHP for testing with PHPUnit as the underlying framework
- Test files located in `tests/Unit` and `tests/Feature`
- Helper functions available in `tests/Pest.php`

### Database
- Uses soft deletes on Account model
- Comprehensive logging for audit trails
- Foreign key relationships between accounts, phones, and related entities

### Queue Processing
- Redis-based queues with Horizon for monitoring
- Unique jobs prevent duplicate processing
- Specialized queues for different job types

### Security Considerations
- Rate limiting on API endpoints
- IP blacklisting capabilities
- User validity checks
- Audit logging for all operations
- Proxy rotation for external API calls

## Admin Panel Access

- Default route: `/admin`
- Uses Filament v3 for admin interface
- Role-based permissions with Spatie Laravel Permission
- Resources for managing accounts, phones, users, and configurations